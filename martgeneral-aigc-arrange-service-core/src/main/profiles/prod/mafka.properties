mdp.mafka.producer[0].producerName=asyncRunFlowProducer
mdp.mafka.producer[0].bgNameSpace=pingtai
mdp.mafka.producer[0].appkey=com.sankuai.gaigc.arrange.service
mdp.mafka.producer[0].topicName=gaigc-async-result
#flow call model cost record - mq-producer -config
mdp.mafka.producer[1].producerName=aigcFlowCostCollectRecordProducer
mdp.mafka.producer[1].bgNameSpace=pingtai
mdp.mafka.producer[1].appkey=com.sankuai.gaigc.arrange.service
mdp.mafka.producer[1].topicName=aigc-flow-cost-collect-record
#flow execution log record - mq-producer
mdp.mafka.producer[2].producerName=flowExecutionLogProducer
mdp.mafka.producer[2].bgNameSpace=daozong
mdp.mafka.producer[2].appkey=com.sankuai.gaigc.arrange.service
mdp.mafka.producer[2].topicName=nibaigc-flow-execution-log
#bot execution log record - mq-producer
mdp.mafka.producer[3].producerName=botExecutionLogProducer
mdp.mafka.producer[3].bgNameSpace=hotel
mdp.mafka.producer[3].appkey=com.sankuai.gaigc.arrange.service
mdp.mafka.producer[3].topicName=nibaigc-bot-execution-log
#function execution log record - mq-producer
mdp.mafka.producer[4].producerName=funcExecutionLogProducer
mdp.mafka.producer[4].bgNameSpace=hotel
mdp.mafka.producer[4].appkey=com.sankuai.gaigc.arrange.service
mdp.mafka.producer[4].topicName=nibaigc-function-execution-log
#model degrade notification - mq-producer
mdp.mafka.producer[5].producerName=aigcModelDegradeNotificationProducer
mdp.mafka.producer[5].bgNameSpace=hotel
mdp.mafka.producer[5].appkey=com.sankuai.gaigc.arrange.service
mdp.mafka.producer[5].topicName=fuxi_model_degrade_notification
#flow call model cost record - mq-consumer -config
mdp.mafka.consumer[0].bgNameSpace=pingtai
mdp.mafka.consumer[0].appkey=com.sankuai.gaigc.arrange.service
mdp.mafka.consumer[0].topicName=aigc-flow-cost-collect-record
mdp.mafka.consumer[0].subscribeGroup=aigc-flow-cost-collect-record-consumer
mdp.mafka.consumer[0].listenerId=aigcFlowCostCollectRecordListener
mdp.mafka.consumer[0].batchRecv=true
mdp.mafka.consumer[0].batchRecvMsgClass=java.lang.String
mdp.mafka.consumer[0].batchRecvSize=100

mdp.mafka.consumer[1].bgNameSpace=hotel
mdp.mafka.consumer[1].appkey=com.sankuai.gaigc.arrange.service
mdp.mafka.consumer[1].topicName=fuxi.bot.waihu.muxing.callback
mdp.mafka.consumer[1].subscribeGroup=fuxi.bot.waihu.muxing.callback.group
mdp.mafka.consumer[1].listenerId=botWaihuCallBackListener