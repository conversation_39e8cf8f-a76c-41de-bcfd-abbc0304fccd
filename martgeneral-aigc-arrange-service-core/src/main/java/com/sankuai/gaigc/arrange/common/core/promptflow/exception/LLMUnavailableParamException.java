package com.sankuai.gaigc.arrange.common.core.promptflow.exception;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @created: 2024/5/23
 * @description:
 */
public class LLMUnavailableParamException extends RuntimeException{

    private int errorCode;
    public LLMUnavailableParamException(String message) {
        super(message);
    }
    public LLMUnavailableParamException(String message, int errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public LLMUnavailableParamException(Throwable cause) {
        super(cause);
    }
    public LLMUnavailableParamException(String message, Throwable cause) {
        super(message, cause);
    }
}
