@startuml

class AIBot {
    - Long id
    - List<Plugin> plugins
    - List<KnowledgeBase> knowledgeBases
    - String characterPrompt
    - String minorPrompt
    - BotModelConfig modelConfig
    - IAgent agent
    - List<BotVariableItem> botVariables
    - List<? extends BotEnhanceItem> botEnhances
    
    + BotRunResult processQueryByPlan(BotRunParam runParam)
}

interface IAgent {
    + void onStart(BotRunParam runParam, AIBot bot)
    + BotRunResult run(BotRunParam runParam, AIBot bot)
    + void onError(BotRunParam runParam, AIBot bot, Exception e)
    + void onSuccess(BotRunResult runResult, BotRunParam runParam, AIBot bot)
    + AgentModeEnum mode()
}

class AbstractAgent implements IAgent {
    - List<IAgentAspectProcessor> agentAspectProcessors
    + abstract ExecutePlan makePlan(BotRunParam runParam, AIBot bot)
    + abstract BotRunResult parseResult(ExecutePlan plan, BotRunParam runParam, AIBot bot)
    + BotRunResult run(BotRunParam runParam, AIBot bot)
    + void onStart(BotRunParam runParam, AIBot bot)
    + void onError(BotRunParam runParam, AIBot bot, Exception e)
    + void onSuccess(BotRunResult runResult, BotRunParam runParam, AIBot bot)
}

interface IAgentAspectProcessor {
    + void whenStart(BotRunParam runParam, AIBot bot)
    + void whenError(BotRunParam runParam, AIBot bot, Exception e)
    + void whenSuccess(BotRunResult runResult, BotRunParam runParam, AIBot bot)
}

class BotEnhanceProcessor implements IAgentAspectProcessor {
    - List<BotStartEnhanceHandler> startEnhanceHandlers
    - List<BotSuccessEnhanceHandler> successEnhanceHandlers
    + void whenStart(BotRunParam runParam, AIBot bot)
    + void whenError(BotRunParam runParam, AIBot bot, Exception e)
    + void whenSuccess(BotRunResult runResult, BotRunParam runParam, AIBot bot)
}

interface BotStartEnhanceHandler {
    + void handle(BotEnhanceItem enhanceConfig, BotRunParam runParam, AIBot bot)
    + String relateConfigClassName()
}

interface BotSuccessEnhanceHandler {
    + void handle(BotEnhanceItem enhanceConfig, BotRunResult runResult, BotRunParam runParam, AIBot bot)
    + String relateConfigClassName()
}
class AnswerReferenceEnhanceHandler implements BotSuccessEnhanceHandler {}
class QueryRecommendEnhanceHandler implements BotSuccessEnhanceHandler {}

AIBot <.. IAgent
AbstractAgent <.. IAgentAspectProcessor
BotEnhanceProcessor <.. BotStartEnhanceHandler
BotEnhanceProcessor <.. BotSuccessEnhanceHandler
@enduml