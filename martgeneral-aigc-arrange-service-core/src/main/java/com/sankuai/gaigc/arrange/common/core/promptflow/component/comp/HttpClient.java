package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.meituan.mtrace.Tracer;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.function.GenericHttpService;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: fanghehe
 * @Description:
 * @Date: 2023-10-26 15:25
 */
@PromptFlowComponent(name = "HttpClient", desc = "http接口调用组件", type = ComponentTypeEnum.SERVICE_CALL, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "appKey", desc = "服务端appKey", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "url", desc = "http请求地址", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "method", desc = "http请求方法", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "params", desc = "接口入参", type = ParamTypeEnum.MAP, category = CategoryEnum.CUSTOM, required = false),
        @Param(name = "headers", desc = "http请求headers", type = ParamTypeEnum.MAP, category = CategoryEnum.CUSTOM, required = false),
        @Param(name = "timeout", desc = "超时时间，单位ms，默认为10000ms", type = ParamTypeEnum.INT, category = CategoryEnum.DEFAULT, required = false)
})
@OutputParamDefinition({@Param(name = "result", desc = "http接口返回结果", type = ParamTypeEnum.DEFAULT, category = CategoryEnum.DEFAULT, required = true)})
@Slf4j
public class HttpClient extends AbstractComponent {

    @Autowired
    private GenericHttpService httpService;

    public static final String POST_METHOD = "post";

    public static final String GET_METHOD = "get";

    @Override
    public Map<String, Object> execute (Context context, ComponentInfo componentInfo) throws Exception {
        String appKey = (String) parseParam("appKey", componentInfo);
        String url = (String) parseParam("url", componentInfo);
        String method = (String) parseParam("method", componentInfo);
        Integer timeout = Optional.ofNullable((Integer) parseParam("timeout", componentInfo)).orElse(10000);
        Map<String, Object> paramMap = (Map<String, Object>) parseParam("params", componentInfo);
        Map<String, String> headerMap = (Map<String, String>) parseParam("headers", componentInfo);
        if (Objects.isNull(headerMap)) {
            headerMap = new HashMap<>();
        }
        //透传trace id
        headerMap.put("M-Traceid", Tracer.id());
        String httpResult = "";
        if(Objects.equals(POST_METHOD, method)) {
            httpResult = httpService.sendPost(url, JSON.toJSONString(paramMap), headerMap, timeout);
        } else if(Objects.equals(GET_METHOD, method)) {
            if (paramMap == null || paramMap.isEmpty()) {
                httpResult = httpService.sendGet(url, headerMap, timeout);
            }else {
                UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(url);
                MultiValueMap<String, String> existingQueryParams = new LinkedMultiValueMap<>(builder.build().getQueryParams());

                for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    if (value instanceof Collection) {
                        Collection<?> values = (Collection<?>) value;
                        existingQueryParams.put(key, values.stream().map(Object::toString).collect(Collectors.toList()));
                    } else {
                        existingQueryParams.put(key, Collections.singletonList(value.toString()));
                    }
                }

                builder.replaceQueryParams(existingQueryParams);
                UriComponents updatedUriComponents = builder.build();
                updatedUriComponents.encode();
                String finalUrl = updatedUriComponents.toUriString();
                httpResult = httpService.sendGet(finalUrl, headerMap, timeout);
            }
        }

        HashMap<String, Object> result = Maps.newHashMap();
        result.put("result", httpResult);
        return result;
    }
}

