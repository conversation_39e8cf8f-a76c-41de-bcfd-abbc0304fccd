package com.sankuai.gaigc.arrange.common.core.bot.stream;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.meituan.mtrace.Tracer;
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlanContext;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecuteStep;
import com.sankuai.gaigc.arrange.common.core.bot.utils.FlowResultUtils;
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils;
import com.sankuai.gaigc.arrange.common.core.promptflow.function.FunctionCallNotExecuteResult;
import com.sankuai.gaigc.arrange.common.model.SSEMessage;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

@Component
@Slf4j
public class IntentExecuteStreamHandler implements IBotStreamHandler {
    @Override
    public void onStart(ExecuteStep step, ExecutePlanContext context) {
        BotRunParam runParam = context.getRunParam();
        if (!StreamInfoHolder.availableStream(runParam.getStream()) || !runParam.isDebug()) {
            return;
        }

        try {
            ExecuteStep intentRecognizerStep = context.getExecutionSteps().get(0).getSteps().get(0);
            ExecuteResult executeResult = intentRecognizerStep.getExecuteResult();
            FunctionCallNotExecuteResult answer = parseFunctionResult(executeResult);
            String functionName = answer.getName();

            SSEMessage sseMessage = SSEMessageUtils.generateFunctionProcessStart(functionName, GsonUtil.toJson(answer.getArguments(), Map.class), step.getSectionId());
            SSEMessageUtils.botFirstTokenMetric(context.getBot(), sseMessage);
            StreamInfoHolder.getEmitter().send(sseMessage);
        } catch (IOException e) {
            log.error("意图执行流式输出出错, executeRequest:{}, e=", GsonUtil.toJson(step.getCustomParam()), e);
        }
    }

    public FunctionCallNotExecuteResult parseFunctionResult(ExecuteResult executeResult) {
        try {
            FunctionCallNotExecuteResult answer = JSONObject.parseObject((String) executeResult.getResult().get("answer"), FunctionCallNotExecuteResult.class);
            return answer;
        } catch (JSONException e) {
            FunctionCallNotExecuteResult answer = new FunctionCallNotExecuteResult();
            answer.setIntent("未知意图");
            answer.setName("未知意图 RAG链路回复");
            answer.setArguments(executeResult.getResult());
            return answer;
        }
    }

    @Override
    public void onError(ExecuteStep step, ExecutePlanContext context, Exception executorError) {
        BotRunParam runParam = context.getRunParam();
        if (!StreamInfoHolder.availableStream(runParam.getStream())) {
            return;
        }

        long timeCost = System.currentTimeMillis() - step.getStartTime();

        Map<String, Object> result = new HashMap<>();
        result.put("traceId", Tracer.id());

        if (runParam.isDebug()) {
            SSEMessage sseMessage = SSEMessageUtils.generateFunctionProcessEnd(step.getExecuteTool(), GsonUtil.toJson(step.getCustomParam()), GsonUtil.toJson(result), timeCost, step.getSectionId());
            sseMessage.setError(executorError.getMessage());
            SSEMessageUtils.botFirstTokenMetric(context.getBot(), sseMessage);
            try {
                StreamInfoHolder.getEmitter().send(sseMessage);
            } catch (IOException e) {
                log.error("流式输出出错, executorResult:{}, e=", e);
            }
        }

        SSEMessage sseMessage = SSEMessageUtils.generateFlowRunResult(result, timeCost, step.getSectionId(),true);
        SSEMessageUtils.botFirstTokenMetric(context.getBot(), sseMessage);
        try {
            StreamInfoHolder.getEmitter().send(sseMessage);
        } catch (IOException e) {
            log.error("流式输出出错, e=", e);
        }
    }

    @Override
    public void onSuccess(ExecuteResult executorResult, ExecuteStep step, ExecutePlanContext context) {
        BotRunParam runParam = context.getRunParam();
        if (!StreamInfoHolder.availableStream(runParam.getStream())) {
            return;
        }
        Map<String, Object> result = executorResult.getResult();
        double second = (double) (System.currentTimeMillis() - step.getStartTime()) / 1000.0D;

        //流式输出Flow结果
        if ((runParam.isDebug() && StreamInfoHolder.get() != null && !StreamInfoHolder.get().getLlmOutputFlag())) {
            String modelReply = FlowResultUtils.parseModelReplayFromFlowResult(result);
            if (Objects.nonNull(modelReply)) {
                try {
                    SSEMessage sseMessage = SSEMessageUtils.generateDirectAnswerMessage(modelReply, step.getSectionId(), true);
                    StreamInfoHolder.getEmitter().send(sseMessage);
                } catch (IOException e) {
                    log.error("流式输出出错，modelReplay=" + modelReply, e);
                }
            }
        }

        if (runParam.isDebug()) {
            if (Objects.isNull(result)) {
                result = new HashMap<>();
            }
            result.put("traceId", Tracer.id());
            SSEMessage sseMessage = SSEMessageUtils.generateFunctionProcessEnd(step.getExecuteTool(), GsonUtil.toJson(step.getCustomParam()), GsonUtil.toJson(result), second, step.getSectionId());
            SSEMessageUtils.botFirstTokenMetric(context.getBot(), sseMessage);
            try {
                StreamInfoHolder.getEmitter().send(sseMessage);
            } catch (IOException e) {
                log.error("流式输出出错, executorResult:{}, e=", executorResult, e);
            }
        }
        SSEMessage sseMessage = SSEMessageUtils.generateFlowRunResult(result, second, step.getSectionId(),false);
        try {
            if (Objects.isNull(executorResult.getBotCardResult())) {
                sseMessage.setIs_last_one(true);
            }
            StreamInfoHolder.getEmitter().send(sseMessage);
            SSEMessageUtils.botFirstTokenMetric(context.getBot(), sseMessage);

            if (Objects.nonNull(executorResult.getBotCardResult())) {
                SSEMessage sseMessageCardRes = SSEMessageUtils.generateBotCardResult(executorResult.getBotCardResult(), second, step.getSectionId());
                StreamInfoHolder.getEmitter().send(sseMessageCardRes);
            }
        } catch (IOException e) {
            log.error("流式输出出错，executorResult:{}, e=", executorResult, e);
        }
    }

    @Override
    public BotExecutorTypeEnum type() {
        return BotExecutorTypeEnum.INTENT_EXECUTE;
    }
}
