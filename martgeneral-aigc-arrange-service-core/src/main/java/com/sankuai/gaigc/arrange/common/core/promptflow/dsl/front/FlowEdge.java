package com.sankuai.gaigc.arrange.common.core.promptflow.dsl.front;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/7
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlowEdge implements Serializable {
    String instanceId;
    String instanceName;
    String sourceId;
    String sourceName;
    String sourceHandle;
    String targetId;
    String targetName;
    String targetHandle;
}
