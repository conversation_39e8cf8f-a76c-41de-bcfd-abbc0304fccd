package com.sankuai.gaigc.arrange.common.core.promptflow.util;

import com.dianping.cat.Cat;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class CatLogUtils {
    public static final String COM_TYPE_PRE = "Comp_";
    public static void logComponent(ComponentInfo componentInfo) {
        if (Objects.nonNull(componentInfo) && StringUtils.isNotEmpty(componentInfo.getFlowId())) {
            Cat.logEvent(COM_TYPE_PRE + componentInfo.getComponentName(), componentInfo.getFlowId());
        } else {
            Cat.logEvent(COM_TYPE_PRE + componentInfo.getComponentName(), "-");
        }
    }
}
