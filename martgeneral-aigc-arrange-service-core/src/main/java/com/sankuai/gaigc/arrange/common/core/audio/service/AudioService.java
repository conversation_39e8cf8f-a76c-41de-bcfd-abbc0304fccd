package com.sankuai.gaigc.arrange.common.core.audio.service;

import com.sankuai.gaigc.arrange.common.core.audio.entity.AudioEntity;
import com.sankuai.gaigc.arrange.api.entity.audio.TTSRequest;
import com.sankuai.gaigc.arrange.api.entity.audio.TTSResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/24 17:44
 * @desc
 */
public interface AudioService {

    TTSResult text2Audio(TTSRequest audioRequest);

    Map<TTSRequest,TTSResult> batchText2Audio(List<TTSRequest> audioRequests);

    List<AudioEntity> getOnLineAudioList();
}
