package com.sankuai.gaigc.arrange.common.core.bot.service;


import com.sankuai.gaigc.arrange.api.entity.bot.ChatMessageDto;
import com.sankuai.gaigc.arrange.api.thrift.Param;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.UserModel;

import java.util.List;
import java.util.Map;

public interface AIBotExecuteService {

    void streamExecuteBot(Long botId, Integer version, String userMsg, List<String> imgUrls, Map<String, Param> inputParam, UserModel user,boolean debug,List<ChatMessageDto> bizMsgHistory) throws Exception;
    Map<String, Object> runBot(BotRunParam runParam) throws Exception;
}
