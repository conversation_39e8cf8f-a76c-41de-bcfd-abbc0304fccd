package com.sankuai.gaigc.arrange.common.core.pigeon.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.pigeon.remoting.common.util.ContextUtils;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.meituan.mtrace.Tracer;
import com.sankuai.gaigc.arrange.api.callback.PromptFlowCallback;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.entity.DebugFlowRequest;
import com.sankuai.gaigc.arrange.api.entity.Request;
import com.sankuai.gaigc.arrange.api.entity.Response;
import com.sankuai.gaigc.arrange.api.enums.ErrorCodeEnum;
import com.sankuai.gaigc.arrange.api.enums.FlowRunModeEnum;
import com.sankuai.gaigc.arrange.api.service.PromptFlowExecutionService;
import com.sankuai.gaigc.arrange.common.constant.TracerConstants;
import com.sankuai.gaigc.arrange.common.core.promptflow.beans.PromptFlowManager;
import com.sankuai.gaigc.arrange.common.core.promptflow.callback.InnerEngineCallback;
import com.sankuai.gaigc.arrange.common.core.promptflow.componentdebug.IFunctionClientDebug;
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ResponseConstants;
import com.sankuai.gaigc.arrange.common.core.promptflow.dsl.Flow;
import com.sankuai.gaigc.arrange.common.core.promptflow.engine.PromptFlowEngine;
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.AsyncRunFlowEntity;
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.dto.FlowRunContextDTO;
import com.sankuai.gaigc.arrange.common.core.promptflow.enums.AsyncRunFlowStatusEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.GrayPublishUtils;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.ResponseUtils;
import com.sankuai.gaigc.arrange.common.enums.AppEntityTypeEnum;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/9
 */
@MdpPigeonServer
public class PromptFlowExecutionServiceImpl implements PromptFlowExecutionService {
    @Autowired
    PromptFlowEngine promptFlowEngine;
    @Autowired
    PromptFlowManager promptFlowManager;
    @Resource
    private IFunctionClientDebug functionClientDebug;

    public static final String PIGEON_CLIENT_APP = "CLIENT_APP";


    @Override
    public Response<String> runFlowWithCallBack (Long flowId, Map<String, Object> inputParams, Map<String, Object> envMap, PromptFlowCallback callback) {
        Flow flow = promptFlowManager.getFlowByIdIfItIsInTheCache(flowId);
        return ResponseUtils.createSuccessResponse(runFlow(flow, inputParams, envMap, callback));
    }

    @Override
    public Response<Map<String, Object>> runFlow(Long flowId, Map<String, Object> inputs) {
        Flow flowEntity = null;

        //250218 增加“灰度发布”判断 如果正在灰度中 用灰度版本替换
        Integer grayVersion = GrayPublishUtils.getGrayPublishVersion(AppEntityTypeEnum.FLOW, flowId);
        if (grayVersion != null) {
            flowEntity = promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(flowId, grayVersion);
        } else {
            flowEntity = promptFlowManager.getFlowByIdIfItIsInTheCache(flowId);
        }
        return ResponseUtils.createSuccessResponse(runFlow(flowEntity, inputs, null, FlowRunModeEnum.SYNC));
    }

    @Override
    public Response<Map<String, Object>> runFlow(Long flowId, Map<String, Object> inputs, Map<String, Object> envMap) {
        Flow flowEntity = null;

        //250218 增加“灰度发布”判断 如果正在灰度中 用灰度版本替换
        Integer grayVersion = GrayPublishUtils.getGrayPublishVersion(AppEntityTypeEnum.FLOW, flowId);
        if (grayVersion != null) {
            flowEntity = promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(flowId, grayVersion);
        } else {
            flowEntity = promptFlowManager.getFlowByIdIfItIsInTheCache(flowId);
        }
        return ResponseUtils.createSuccessResponse(runFlow(flowEntity, inputs, envMap, FlowRunModeEnum.SYNC));
    }
    @Override
    public Response<Map<String, Map<String, Object>>> debugFlow(Long flowId, String interruptName, Map<String, Object> inputParams) {
        FlowRunContextDTO flowRunContextDTO = Optional.ofNullable(Tracer.getContext(TracerConstants.FLOW_RUN_CONTEXT)).map(x -> JSON.parseObject(x, FlowRunContextDTO.class))
                .orElse(new FlowRunContextDTO());
        flowRunContextDTO.setIsDebugFlow(true);
        Tracer.putContext(TracerConstants.FLOW_RUN_CONTEXT, JSON.toJSONString(flowRunContextDTO));
        Flow flow = promptFlowManager.getFlowByIdIfItIsInTheCache(flowId);
        if (StringUtils.isBlank(interruptName)) {
            interruptName = "outputs";
        }
        return ResponseUtils.createSuccessResponse(debugFlow(flow, interruptName, inputParams));
    }

    @Override
    public Response<Map<String, Object>> runFlow(Request request) {
        Flow flowEntity = promptFlowManager.getFlowByIdIfItIsInTheCache(request.getFlowId());
        FlowRunModeEnum runMode = FlowRunModeEnum.valueOf(request.getRunMode());
        Map<String, Object> envMap = null;
        if (!MapUtils.isEmpty(request.getBizParams())) {
            envMap = new HashMap<>();
            envMap.put("bizParams", request.getBizParams());
        }
        Map<String, Object> runFlowResult = runFlow(flowEntity, request.getInputParams(), envMap, runMode);
        if (FlowRunModeEnum.ASYNC == runMode) {
            return ResponseUtils.createAsyncSuccessResponse((String) runFlowResult.get(ResponseConstants.TASK_ID));
        }
        return ResponseUtils.createSuccessResponse(runFlowResult);
    }

    @Override
    public Response<Map<String, Object>> getRunFlowAsyncResult(String taskId) {

        AsyncRunFlowEntity runFlowAsyncResult = promptFlowEngine.getRunFlowAsyncResult(taskId);
        if (Objects.isNull(runFlowAsyncResult)) {
            return ResponseUtils.createFailedResponse(ErrorCodeEnum.ASYNC_RESULT_NOT_FOUND, ErrorCodeEnum.ASYNC_RESULT_NOT_FOUND.getDesc());
        }
        AsyncRunFlowStatusEnum status = AsyncRunFlowStatusEnum.valueOf(runFlowAsyncResult.getStatus());
        if (AsyncRunFlowStatusEnum.ERROR == status) {
            return ResponseUtils.createFailedResponse(ErrorCodeEnum.INNER_ERROR, runFlowAsyncResult.getMsg());
        }
        if (AsyncRunFlowStatusEnum.RUNNING == status) {
            return ResponseUtils.createFailedResponse(ErrorCodeEnum.ASYNC_FLOW_RUNNING, ErrorCodeEnum.ASYNC_FLOW_RUNNING.getDesc());
        }
        return ResponseUtils.createSuccessResponse(runFlowAsyncResult.getRunFlowResponse(), runFlowAsyncResult.getTraceId());
    }

    public Map<String, Object> runFlow (Flow flow, Map<String, Object> inputParams, Map<String, Object> envMap,FlowRunModeEnum runModeEnum) {
        if (FlowRunModeEnum.ASYNC == runModeEnum) {
            return promptFlowEngine.runFlowAsync(flow, inputParams, envMap, (String) ContextUtils.getLocalContext(PIGEON_CLIENT_APP));
        }
        return promptFlowEngine.runFlow(flow, inputParams, envMap, runModeEnum);
    }

    private Map<String, Map<String, Object>> debugFlow (Flow flow, String interruptName, Map<String, Object> inputParams) {
        return promptFlowEngine.debugFlow(flow, interruptName, inputParams, null);
    }

    private String runFlow (Flow flow, Map<String, Object> inputParams, Map<String, Object> envMap, PromptFlowCallback callback) {
        final InnerEngineCallback innerEngineCallback = new InnerEngineCallback(callback);
        return promptFlowEngine.runFlowWithCallBack(flow, inputParams, envMap, innerEngineCallback);
    }

    @Override
    public Response<Map<String, Object>>  runFlowStream(Long flowId, Map<String, Object> inputParams, Map<String, Object> envMap, SseEmitter emitter) {
        Flow flow = null;
        //250218 增加“灰度发布”判断 如果正在灰度中 用灰度版本替换
        Integer grayVersion = GrayPublishUtils.getGrayPublishVersion(AppEntityTypeEnum.FLOW, flowId);
        if (grayVersion != null) {
            flow = promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(flowId, grayVersion);
        } else {
            flow = promptFlowManager.getFlowByIdIfItIsInTheCache(flowId);
        }
        return ResponseUtils.createSuccessResponse(promptFlowEngine.runFlowStream(flow,inputParams, envMap, emitter));
    }

    @Override
    public Response<Map<String, Map<String, Object>>> debugFlow(DebugFlowRequest request) {
        FlowRunContextDTO flowRunContextDTO = Optional.ofNullable(Tracer.getContext(TracerConstants.FLOW_RUN_CONTEXT)).map(x -> JSON.parseObject(x, FlowRunContextDTO.class))
                .orElse(new FlowRunContextDTO());
        flowRunContextDTO.setIsDebugFlow(true);
        Tracer.putContext(TracerConstants.FLOW_RUN_CONTEXT, JSON.toJSONString(flowRunContextDTO));
        //未传版本信息时运行线上版本；否则按版本运行
        Flow flow = (Objects.isNull(request.getVersion()) || request.getVersion() == 0) ? promptFlowManager.getFlowByIdIfItIsInTheCache(request.getFlowId()) :
                promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(request.getFlowId(), request.getVersion());
        if (StringUtils.isBlank(request.getInterruptName())) {
            request.setInterruptName("outputs");
        }
        return ResponseUtils.createSuccessResponse(
                debugFlow(flow, request.getInterruptName(), request.getInputParams()));
    }

    @Override
    public Response<Map<String, Object>> runFunctionDraft(Integer functionId, Map<String, Object> inputParams, Map<String, Object> headerParams, Integer timeout) {
        return ResponseUtils.createSuccessResponse(functionClientDebug.execute(Long.valueOf(functionId), 0, inputParams, headerParams, timeout));
    }

    @Override
    public Response<Map<String, Object>> runFunction(Context context, Integer functionId, Map<String, Object> inputParams, Map<String, Object> headerParams, Integer timeout) throws Exception {
        return ResponseUtils.createSuccessResponse(functionClientDebug.execute(Long.valueOf(functionId), 0, inputParams, headerParams, timeout));
    }
}
