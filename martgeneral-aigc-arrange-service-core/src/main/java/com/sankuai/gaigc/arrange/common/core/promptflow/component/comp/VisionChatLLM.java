package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Maps;
import com.meituan.mdp.langmodel.api.memory.ChatMemory;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.message.interfaces.VisionContent;
import com.meituan.mdp.langmodel.api.message.vision.AssistantVisionMessage;
import com.meituan.mdp.langmodel.api.message.vision.ImageVisionContent;
import com.meituan.mdp.langmodel.api.message.vision.TextVisionContent;
import com.meituan.mdp.langmodel.api.message.vision.UserVisionMessage;
import com.meituan.mdp.langmodel.api.store.memory.ChatMemoryStore;
import com.meituan.mdp.langmodel.component.memory.ConversationChatMemory;
import com.meituan.mdp.langmodel.component.model.chat.vision.OpenAIVisionChatModel;
import com.meituan.mdp.langmodel.component.properties.model.friday.FridayModelProperties;
import com.meituan.mdp.langmodel.component.utils.ModelHelper;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.*;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ComponentConstants;
import com.sankuai.gaigc.arrange.common.core.promptflow.exception.FridayLLMModelException;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.RhinoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@PromptFlowComponent(name = "VisionChatLLM", desc = "图片交流大模型", type = ComponentTypeEnum.LLM, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "model_name", desc = "图片模型名称，默认gpt-4-vision-preview", type = ParamTypeEnum.STRING, required = false, category = CategoryEnum.ADVANCED),
        @Param(name = "user_prompt", desc = "prompt组件的output", type = ParamTypeEnum.STRING, required = true, category = CategoryEnum.DEFAULT),
        @Param(name = "app_id", desc = "租户ID，默认使用公共app_id", type = ParamTypeEnum.STRING, required = false, category = CategoryEnum.DEFAULT),

        @Param(name = "max_tokens", desc = "在响应中生成的最大令牌数。 默认为 inf。", type = ParamTypeEnum.INT, required = false, category = CategoryEnum.ADVANCED),
        @Param(name = "temperature", desc = "生成的文本的随机性。 默认值为 1", type = ParamTypeEnum.DOUBLE, required = false, category = CategoryEnum.ADVANCED),
        @Param(name = "top_p", desc = "使用所生成令牌中的最高选项的概率。 默认值为 1。", type = ParamTypeEnum.DOUBLE, required = false, category = CategoryEnum.ADVANCED),
        @Param(name = "presence_penalty", desc = "控制模型与生成的罕见短语相关的行为的值。 默认为 0。", type = ParamTypeEnum.DOUBLE, required = false, category = CategoryEnum.ADVANCED),
        @Param(name = "frequency_penalty", desc = "大模型参数", type = ParamTypeEnum.DOUBLE, required = false, category = CategoryEnum.ADVANCED),
        @Param(name = "chat_memory_enable", desc = "使用会话记忆", type = ParamTypeEnum.BOOLEAN, required = false, category = CategoryEnum.DEFAULT),
        @Param(name = "chat_id", desc = "会话ID", type = ParamTypeEnum.STRING, required = false, category = CategoryEnum.DEFAULT),
        @Param(name = "images", desc = "多媒体图片", type = ParamTypeEnum.LIST, required = false, category = CategoryEnum.DEFAULT)
})
@OutputParamDefinition({@Param(name = "answer", desc = "LLM模型调用的返回值", type = ParamTypeEnum.STRING, required = true)})
public class VisionChatLLM  extends AbstractComponent {
    public static final String RHINO_APP_ID_KEY = "APPID";

    @Autowired
    @Qualifier("squirrelChatMemoryStore")
    private ChatMemoryStore squirrelChatMemoryStore;

    public Map<String, Object> execute(Context context, ComponentInfo componentInfo) throws Exception {
        String modelName = Optional.ofNullable((String) parseParam("model_name", componentInfo)).orElse("gpt-4-vision-preview");
        String userPrompt = (String) parseParam("user_prompt", componentInfo);
        String appId = Optional.ofNullable((String) parseParam("app_id", componentInfo)).orElse(ComponentConstants.DEFAULT_APP_ID);

        // max_tokens的最大值不能超过模型上下文长度
        Integer maxTokens = Optional.ofNullable((Integer) parseParam("max_tokens", componentInfo)).orElse(2048);
        Double temperature = Optional.ofNullable((Double) parseParam("temperature", componentInfo)).orElse(1.0);
        Double topP = Optional.ofNullable((Double) parseParam("top_p", componentInfo)).orElse(1.0);
        Double presencePenalty = Optional.ofNullable((Double) parseParam("presence_penalty", componentInfo)).orElse(0.0);
        Double frequencyPenalty = Optional.ofNullable((Double) parseParam("frequency_penalty", componentInfo)).orElse(0.0);
        Boolean chatMemoryEnable = Optional.ofNullable((Boolean) parseParam("chat_memory_enable", componentInfo)).orElse(false);
        String chatId = Optional.ofNullable((String) parseParam("chat_id", componentInfo)).orElse("");
        List<String> imageUrlList = Optional.ofNullable((List<String>) parseParam("images", componentInfo)).orElse(new ArrayList());

        FridayModelProperties properties = ModelHelper.defaultOpenAIVisionChatModelProperties();
        properties.setModel(modelName);
        properties.setMaxTokens(maxTokens);
        properties.setTemperature(temperature);
        properties.setTopP(topP);
        properties.setPresencePenalty(presencePenalty);
        properties.setFrequencyPenalty(frequencyPenalty);
        properties.setTimeout(0);
        //生成请求信息
        UserVisionMessage userMessage = genUserVisionMessage(userPrompt,imageUrlList);

        long startTime = System.currentTimeMillis();
        Transaction tran = Cat.newTransaction("LLMCall", modelName);

        AssistantVisionMessage assistantMessage = null;
        ChatMemory chatMemory = null;
        List<Message> chatMessages = null;
        if (chatMemoryEnable) {
            chatMemory = new ConversationChatMemory(componentInfo.getFlowId() + ":" + chatId, squirrelChatMemoryStore);
            chatMessages = assembleChatMessage(chatMemory, userMessage);
        }

        try {
            if (!context.getRunMode().getType().equals(FlowRunModeEnum.SYNC.getType())) {
                //批任务、异步调用、debug，可以不限时阻塞至friday满足调用rpm限制
                Map<String, String> reqParam = new HashMap<>();
                reqParam.put(RHINO_APP_ID_KEY, appId);
                RhinoUtils.acquire(modelName, reqParam);
            }

            OpenAIVisionChatModel openAIChatVisionModel = new OpenAIVisionChatModel(appId, properties);
            assistantMessage = chatMemoryEnable ? openAIChatVisionModel.sendMessages(chatMessages): openAIChatVisionModel.sendMessages(userMessage);

            if (chatMemoryEnable) {
                storeHistoryChat(chatMemory, userMessage, assistantMessage);
            }
            //rpcSuccessLog("chatVisionModel", componentInfo, genRpcParam(userMessage), genRpcParam(assistantMessage), startTime);
            logToken(assistantMessage.getUsage(),componentInfo.getFlowId(), componentInfo.getAppId());
        } catch (Exception e) {
            //rpcErrorLog("chatVisionModel", componentInfo, genRpcParam(userMessage), null, startTime, e);
            tran.setStatus(e);
            throw new FridayLLMModelException(String.format("Friday大模型请求异常，appid:%s，modelName:%s，e:%s", appId, modelName, e.getMessage()));
        } finally {
            tran.complete();
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("answer", assistantMessage != null ? JSONArray.parseArray(JSON.toJSONString(assistantMessage.getContent())) : null);
        return map;
    }

    protected UserVisionMessage genUserVisionMessage(String userPrompt, List<String> imageUrlList){
        List<VisionContent> imageMessages = new ArrayList<>();
        if(StringUtils.isNotBlank(userPrompt)){
            imageMessages.add(TextVisionContent.from(userPrompt));
        }

        List<ImageVisionContent> imageVisionContents = imageUrlList.stream()
                .filter(url -> StringUtils.isNotBlank(url))
                .map(url -> ImageVisionContent.from(url))
                .collect(Collectors.toList());
        imageMessages.addAll(imageVisionContents);

        return UserVisionMessage.from(imageMessages);
    }

    private List<Message> assembleChatMessage(ChatMemory chatMemory, Message userMessage) {
        List<Message> chatMessages = new ArrayList<>();
        chatMessages.addAll(chatMemory.messages()); //拼接历史会话
        chatMessages.add(userMessage);              //拼接本次用户输入
        return chatMessages;
    }

    private void storeHistoryChat(ChatMemory chatMemory, UserVisionMessage userMessage, AssistantVisionMessage assistantMessage) {
        chatMemory.add(userMessage);
        chatMemory.add(new AssistantVisionMessage(assistantMessage.getContent()));
    }
}
