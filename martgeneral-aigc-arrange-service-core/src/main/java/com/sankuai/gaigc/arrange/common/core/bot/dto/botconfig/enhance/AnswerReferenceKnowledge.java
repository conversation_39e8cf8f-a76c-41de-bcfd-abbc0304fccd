/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance;

import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 答案引用结果
 *
 * <AUTHOR>
 * @created 2024/8/16
 */
@Data
public class AnswerReferenceKnowledge {
    /** 知识库ID */
    private Long knowledgeBaseId;
    /** 知识库名称 */
    private String knowledgeBaseName;
    /** 知识内容 */
    private String knowledgeContent;
    /** 大模型答案与该条知识的相关性分数 */
    private double referenceScore;
    /** 文档ID */
    private Long docId;
    /** 字段数据 */
    private Map<String, Object> fieldData;

    /** 哈希 */
    private transient Long hash;
    /** 知识分类 */
    private transient List<Integer> category;
}