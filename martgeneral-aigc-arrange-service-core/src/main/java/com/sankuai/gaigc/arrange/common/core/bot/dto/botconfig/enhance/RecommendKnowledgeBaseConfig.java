/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance;

import lombok.Data;

/**
 * query推荐召回知识库配置
 *
 * <AUTHOR>
 * @created 2024/8/19
 */
@Data
public class RecommendKnowledgeBaseConfig {
    /** 复用Bot已召回知识，排除"引用和归属"知识 */
    private Boolean reuseBotKnowledge;
    /** 知识库ID，reuseBotKnowledge=true 时知识库ID不可输入 */
    private Long knowledgeBaseId;
    /** 相关性分数 */
    private Double score;
    /** 数量 */
    private Integer topK;
    /** 知识过滤规则 */
    private String knowledgeFilterRule;
    /** 输出字段名 */
    private String outputFieldName;
}