package com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod;

import com.sankuai.dzim.pilot.api.AIBookService;
import com.sankuai.dzim.pilot.api.data.beauty.ai.BookCallbackReq;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class AIBookManage {
    @Autowired
    private AIBookService aiBookManage;

    public boolean callbackBookAI(String request) {
        BookCallbackReq bookCallbackReq = GsonUtil.fromJson(request, BookCallbackReq.class);
        return aiBookManage.callbackBookAI(bookCallbackReq);
    }
}
