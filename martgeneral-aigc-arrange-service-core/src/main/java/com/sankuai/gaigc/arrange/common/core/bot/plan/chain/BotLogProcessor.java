/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.plan.chain;

import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.constants.BotFieldConstant;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.bot.utils.DtoConvert;
import com.sankuai.gaigc.arrange.common.core.promptflow.aspect.BotLogDetailAspect;
import com.sankuai.gaigc.arrange.common.core.promptflow.aspect.ComponentAspect;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Bot日志处理器
 *
 * <AUTHOR>
 * @created 2024/8/16
 */
@Order(999)
@Component
@Slf4j(topic = "botLog")
public class BotLogProcessor implements IAgentAspectProcessor {

    @Override
    public void whenStart(BotRunParam runParam, AIBot bot) {

    }

    @Override
    public void whenError(BotRunParam runParam, AIBot bot, Exception e) {
        Map<String, String> logMap = buildLogMap(runParam, bot);

        logMap.put(BotLogDetailAspect.ERROR, BotLogDetailAspect.errorToJson("BotAgent执行错误", e));

        final String logMsg = XMDLogFormat.build().putTags(logMap).message(SymbolConstant.EMPTY);
        log.info(logMsg);
    }

    @Override
    public void whenSuccess(BotRunResult runResult, BotRunParam runParam, AIBot bot) {
        Map<String, String> logMap = buildLogMap(runParam, bot);
        logMap.put(ComponentAspect.OUTPUT_PARAMS, GsonUtil.toJson(DtoConvert.convertChatBotRunRes(runResult,runParam.getSessionId())));
        Optional.ofNullable(runResult).ifPresent(botRunResult -> logMap.put(BotFieldConstant.FIELD_REPLY_CONTENT, botRunResult.getReplyContent()));
        logMap.put(BotLogDetailAspect.RECALL, String.valueOf(Objects.nonNull(runResult) && runResult.isRecall()));
        // 提问/引用知识的分类
        Optional.ofNullable(runResult).flatMap(botRunResult -> Optional.ofNullable(botRunResult.getEnhanceResult().getReferenceKnowledge())).ifPresent(refKnowledge -> {
            List<Integer> categoryList = refKnowledge.stream().flatMap(item -> item.getCategory().stream()).distinct().collect(Collectors.toList());
            logMap.put(BotLogDetailAspect.CATEGORY, GsonUtil.toJson(categoryList));
        });

        final String logMsg = XMDLogFormat.build().putTags(logMap).message(SymbolConstant.EMPTY);
        log.info(logMsg);
    }

    private Map<String, String> buildLogMap(BotRunParam runParam, AIBot bot) {
        Map<String, String> logMap = Maps.newLinkedHashMap();
        logMap.put(BotLogDetailAspect.TYPE, BotLogDetailAspect.RESULT);
        logMap.put(BotLogDetailAspect.BOT_ID, String.valueOf(runParam.getBotId()));
        //如果被改写了query记录原始的用户输入
        logMap.put(BotLogDetailAspect.QUERY, runParam.queryOriginalUserMsg());
        logMap.put(BotLogDetailAspect.USER_NAME, runParam.getUser().getUserName());
        logMap.put(BotLogDetailAspect.SESSION_ID, runParam.getSessionId());
        Optional.ofNullable(runParam.getBizParams()).ifPresent(objectMap -> logMap.put(BotLogDetailAspect.TAGS, GsonUtil.toJson(objectMap.get("logTags"))));
        logMap.put(ComponentAspect.INPUT_PARAMS, GsonUtil.toJson(runParam));
        logMap.put(ComponentAspect.START_TIME, String.valueOf(bot.getStart()));
        logMap.put(ComponentAspect.END_TIME, String.valueOf(System.currentTimeMillis()));
        return logMap;
    }
}