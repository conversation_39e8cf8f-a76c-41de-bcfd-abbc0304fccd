/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.executable.base;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.reflect.TypeToken;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.common.constant.SymbolConstant;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.CompParamsUtil;
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult;
import com.sankuai.gaigc.arrange.common.core.bot.Function;
import com.sankuai.gaigc.arrange.common.core.bot.constants.SystemVariableConstants;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.ExecutableMatchSkillParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.StepCustomParam;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.executable.IExecutable;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlanContext;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecuteStep;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotSnapshotService;
import com.sankuai.gaigc.arrange.common.core.bot.utils.BotPromptUtil;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.FunctionCalling;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.proxy.IComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ComponentConstants;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.enums.AgentModeEnum;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.config.MccConfig;
import com.sankuai.gaigc.arrange.dao.dal.enums.FunctionTypeEnum;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 匹配技能
 *
 * <AUTHOR>
 * @created 2024/8/6
 */
@Slf4j
@Component
public class ExecutableMatchSkill implements IExecutable {
    /** 兼容用函数名 */
    public static final String SPECIAL_FUNCTION_NAME = "knowledgeCubeRecall";
    /** 执行器结果字段 */
    public static final String FUNCTION_NAME_FIELD = "functionName";
    public static final String ARGUMENTS_FIELD = "arguments";
    public static final String USAGE_FIELD = "usage";
    public static final String FOLLOWUP_FIELD = "followUp";
    @Resource
    private IComponent functionCalling;
    @Resource
    private MccConfig mccConfig;
    @Resource(name = "AIBotSnapshotServiceImpl")
    public AIBotSnapshotService aiBotSnapshotService;

    @Override
    public ExecuteResult execute(ExecuteStep step, ExecutePlanContext context) throws Exception {
        // 执行FunctionCalling组件
        Map<String, Object> executeResult = executeFunctionCalling(step, context);

        // 执行器结果字段
        Map<String, Object> finalResult = Maps.newHashMap();
        Optional.ofNullable(executeResult.get(USAGE_FIELD)).ifPresent(usage -> finalResult.put(USAGE_FIELD, usage));
        Optional.ofNullable(executeResult.get(FOLLOWUP_FIELD)).ifPresent(followUp -> finalResult.put(FOLLOWUP_FIELD, followUp));
        // 可为空
        Optional.ofNullable(executeResult.get(FUNCTION_NAME_FIELD)).ifPresent(functionName -> finalResult.put(FUNCTION_NAME_FIELD, functionName));
        // 可为空
        Optional.ofNullable(executeResult.get(ARGUMENTS_FIELD)).ifPresent(arguments -> finalResult.put(ARGUMENTS_FIELD, arguments));
        ExecuteResult result = new ExecuteResult(true, step.getObjective(), finalResult, SymbolConstant.EMPTY);

        boolean recall = MapUtils.isNotEmpty(executeResult) && executeResult.containsKey(FUNCTION_NAME_FIELD) && !StringUtils.equals(SPECIAL_FUNCTION_NAME, (String) executeResult.get(FUNCTION_NAME_FIELD));
        result.setRecall(recall);
        if (executeResult.containsKey(FUNCTION_NAME_FIELD)) {
            step.setExecuteTool((String) executeResult.get(FUNCTION_NAME_FIELD));
            step.setHitCard(hitCard(context.getBot(), step, aiBotSnapshotService));
        }

        return result;
    }

    @Override
    public BotExecutorTypeEnum type() {
        return BotExecutorTypeEnum.FUNCTION_MATCH;
    }

    @Override
    public Set<AgentModeEnum> applyMode() {
        return Sets.newHashSet(AgentModeEnum.ALL);
    }

    private Map<String, Object> executeFunctionCalling(ExecuteStep step, ExecutePlanContext context) throws Exception {
        AIBot bot = context.getBot();
        BotRunParam runParam = context.getRunParam();

        // FunctionCalling 匹配插件与工作流
        ComponentInfo componentInfo = getComponentInfo(context);
        componentInfo.setComponentName(FunctionCalling.class.getSimpleName());
        Map<String, Object[]> paramInfo = Maps.newHashMap();

        ExecutableMatchSkillParam matchSkillParam = (ExecutableMatchSkillParam) step.getCustomParam();
        // 工作流
        Map<Boolean, List<Function>> belongWorkFlowAndFunctions = bot.getPlugins().stream().flatMap(plugin -> plugin.getFunctionList().stream()).collect(Collectors.groupingBy(function -> FunctionTypeEnum.WORK_FLOW == function.getFuncType()));
        Optional.ofNullable(belongWorkFlowAndFunctions.get(true)).ifPresent(flowFunctions -> paramInfo.put("flows", new Object[]{GsonUtil.toJson(flowFunctions.stream().map(Function::getFlowId).map(Long::parseLong).collect(Collectors.toList())), ParamTypeEnum.LIST}));
        // 插件
        Optional.ofNullable(belongWorkFlowAndFunctions.get(false)).ifPresent(functions -> paramInfo.put("functions", new Object[]{GsonUtil.toJson(functions.stream().map(Function::getId).collect(Collectors.toList())), ParamTypeEnum.LIST}));
        // 指定技能
        Optional.ofNullable(matchSkillParam.getSpecifySkill()).ifPresent(specifySkill -> {
            paramInfo.remove("flows");
            paramInfo.remove("functions");
            Optional.ofNullable(specifySkill.getFunctionId()).ifPresent(functionId -> paramInfo.put("functions", new Object[]{GsonUtil.toJson(Lists.newArrayList(functionId)),ParamTypeEnum.LIST}));
            Optional.ofNullable(specifySkill.getFlowId()).ifPresent(flowId -> paramInfo.put("flows", new Object[]{GsonUtil.toJson(Lists.newArrayList(flowId)),ParamTypeEnum.LIST}));
        });

        fillModelConfig(paramInfo, step);
        // system prompt
        String prompt = fillPrompt(step, context);
        paramInfo.put("systemPrompt", new Object[]{prompt, ParamTypeEnum.STRING});
        //user prompt
        String userPrompt = String.format(BotPromptUtil.USER_PROMPT_FORMAT, runParam.getUserMsg());
        if (runParam.getBizParams().get(SystemVariableConstants.SYS_BOT_USER_IMG_URLS) != null) {
            String imgs = JSON.toJSONString(runParam.getBizParams().get(SystemVariableConstants.SYS_BOT_USER_IMG_URLS));
            userPrompt = userPrompt + String.format(BotPromptUtil.USER_IMGS_FORMAT, imgs);
        }
        paramInfo.put("userPrompt", new Object[]{userPrompt, ParamTypeEnum.STRING});

        Map<String, Object> user = GsonUtil.fromJson(GsonUtil.toJson(runParam.getUser()), new TypeToken<Map<String, Object>>() {
        }.getType());
        paramInfo.put("userInfo", new Object[]{user, ParamTypeEnum.MAP});
        componentInfo.setInputs(CompParamsUtil.buildInput(paramInfo));

        return (Map<String, Object>) functionCalling.execute(getComponentContext(context), componentInfo);
    }

    private void fillModelConfig(Map<String, Object[]> paramInfo, ExecuteStep step) {
        StepCustomParam customParam = step.getCustomParam();
        ExecutableMatchSkillParam matchSkillParam = (ExecutableMatchSkillParam) customParam;
        // 模型名称
        paramInfo.put("modelName", new Object[]{mccConfig.getBotFunctionCallModelName(), ParamTypeEnum.STRING});
        // 应用ID
        paramInfo.put("appId", new Object[]{ComponentConstants.DEFAULT_APP_ID, ParamTypeEnum.STRING});
        paramInfo.put("max_tokens", new Object[]{4096, ParamTypeEnum.INT});

        paramInfo.put("execute", new Object[]{"false", ParamTypeEnum.BOOLEAN});
        paramInfo.put("modelConvertResult", new Object[]{"false", ParamTypeEnum.BOOLEAN});
        Optional.ofNullable(matchSkillParam).ifPresent(config -> {
            paramInfo.put("forceExecuteFunction", new Object[]{String.valueOf(matchSkillParam.isForceMatch()), ParamTypeEnum.BOOLEAN});
            Optional.ofNullable(config.getAppId()).filter(StringUtils::isNotBlank).ifPresent(appId -> paramInfo.put("appId", new Object[]{appId, ParamTypeEnum.STRING}));
            Optional.ofNullable(config.getFridayModelConfig()).ifPresent(fridayModelConfig -> {
                paramInfo.put("modelName", new Object[]{fridayModelConfig.getModelName(), ParamTypeEnum.STRING});
                paramInfo.put("max_tokens", new Object[]{fridayModelConfig.getMaxTokens(), ParamTypeEnum.INT});
                paramInfo.put("temperature", new Object[]{fridayModelConfig.getTemperature(), ParamTypeEnum.DOUBLE});
                paramInfo.put("top_p", new Object[]{fridayModelConfig.getTopP(), ParamTypeEnum.DOUBLE});
                paramInfo.put("presence_penalty", new Object[]{fridayModelConfig.getPresencePenalty(), ParamTypeEnum.DOUBLE});
                paramInfo.put("frequency_penalty", new Object[]{fridayModelConfig.getFrequencyPenalty(), ParamTypeEnum.DOUBLE});
            });
        });
    }

    private String fillPrompt(ExecuteStep step, ExecutePlanContext context) {
        StepCustomParam customParam = step.getCustomParam();
        Preconditions.checkArgument(Objects.nonNull(customParam) && (customParam instanceof ExecutableMatchSkillParam), "执行节点参数无法处理");

        ExecutableMatchSkillParam matchSkillParam = (ExecutableMatchSkillParam) customParam;
        if (Objects.isNull(matchSkillParam.getFillPromptFunction())) {
            return context.getBot().getCharacterPrompt();
        }
        return matchSkillParam.getFillPromptFunction().apply(step, context);
    }
}