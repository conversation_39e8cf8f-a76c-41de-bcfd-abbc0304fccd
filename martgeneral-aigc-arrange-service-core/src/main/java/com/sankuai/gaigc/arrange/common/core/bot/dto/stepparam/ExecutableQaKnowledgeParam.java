package com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam;

import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeResultItem;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class ExecutableQaKnowledgeParam extends StepCustomParam {
    private List<KnowledgeResultItem> qaKnowledgeResultItems;
    private Map<String, String> placeHolderAndContent;
}
