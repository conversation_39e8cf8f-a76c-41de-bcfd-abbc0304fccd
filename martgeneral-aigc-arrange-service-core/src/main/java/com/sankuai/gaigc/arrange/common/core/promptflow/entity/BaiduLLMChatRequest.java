package com.sankuai.gaigc.arrange.common.core.promptflow.entity;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class BaiduLLMChatRequest {
    private List<Message> messages;
    private Double temperature;
    @SerializedName("top_p")
    private Double topP;
    @SerializedName("penalty_score")
    private Double penaltyScore;
    private String system;
    @SerializedName("disable_search")
    private Boolean disableSearch;
    @SerializedName("max_output_tokens")
    private Integer maxTokens;
    private List<String> stop;
    @Data
    public static class Message {
        private String role;
        private String content;
    }
}
