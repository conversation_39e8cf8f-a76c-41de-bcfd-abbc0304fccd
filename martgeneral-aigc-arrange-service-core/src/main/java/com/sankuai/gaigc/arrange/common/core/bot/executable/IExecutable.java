package com.sankuai.gaigc.arrange.common.core.bot.executable;

import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.FlowRunModeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult;
import com.sankuai.gaigc.arrange.common.core.bot.Function;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotCardObjEnum;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlanContext;
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecuteStep;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotSnapshotService;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.enums.AgentModeEnum;
import com.sankuai.gaigc.arrange.dao.dal.entity.BotCardDO;
import com.sankuai.gaigc.arrange.dao.dal.enums.FunctionTypeEnum;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

public interface IExecutable {
    ExecuteResult execute(ExecuteStep step, ExecutePlanContext context) throws Exception;

    /**
     * 执行器类型
     */
    BotExecutorTypeEnum type();

    /**
     * 适用Bot模式
     */
    Set<AgentModeEnum> applyMode();

    default Context getComponentContext(ExecutePlanContext context) {
        Context componentContext = new Context();
        if (StreamInfoHolder.availableStream(context.getRunParam().getStream())) {
            componentContext.setRunMode(FlowRunModeEnum.STREAM);
            componentContext.setEmitter(StreamInfoHolder.getEmitter());
        } else {
            componentContext.setRunMode(FlowRunModeEnum.SYNC);
        }
        componentContext.setBotHistoryMessages(context.getRunParam().getHistoryMessages());
        return componentContext;
    }

    default ComponentInfo getComponentInfo(ExecutePlanContext context) {
        ComponentInfo componentInfo = new ComponentInfo();
        componentInfo.setBotId(String.valueOf(context.getBot().getId()));
        componentInfo.setAppId(String.valueOf(context.getBot().getAppId()));
        return componentInfo;
    }

    default boolean hitCard(AIBot bot, ExecuteStep step, AIBotSnapshotService aiBotSnapshotService) {
        String type = step.getType();
        if (!type.equals(BotExecutorTypeEnum.FUNCTION_MATCH.getType())) {
            return false;
        }
        Optional<Function> matchFunctionOpt = bot.getPlugins().stream().flatMap(plugin -> plugin.getFunctionList().stream()).filter(function -> StringUtils.equals(step.getExecuteTool(), function.getName())).findFirst();
        if (!matchFunctionOpt.isPresent()) {
            return false;
        }
        Function matchFunction = matchFunctionOpt.get();
        if (matchFunction.getFuncType() == FunctionTypeEnum.WORK_FLOW) {
            return false;
        }
        // 查询卡片
        List<BotCardDO> botCardDOList = aiBotSnapshotService.getBotCardConfig(bot.getId());
        Optional<BotCardDO> botCardDOOptional = botCardDOList.stream().filter(botCardDO -> BotCardObjEnum.TOOL.getValue().equals(botCardDO.getCardObj())).filter(botCardDO -> botCardDO.getObjId().equals(matchFunction.getId())).findFirst();
        return botCardDOOptional.isPresent() && BooleanUtils.isTrue(botCardDOOptional.get().getEnable());
    }

}

