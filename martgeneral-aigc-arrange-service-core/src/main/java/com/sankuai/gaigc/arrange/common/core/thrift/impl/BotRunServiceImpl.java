package com.sankuai.gaigc.arrange.common.core.thrift.impl;

import com.dianping.zebra.util.StringUtils;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.gaigc.arrange.api.entity.bot.BotRunRequest;
import com.sankuai.gaigc.arrange.api.entity.bot.BotRunResponse;
import com.sankuai.gaigc.arrange.api.enums.FuxiCallSourceEnum;
import com.sankuai.gaigc.arrange.api.thrift.BotRunService;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.ArenaAbTestService;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.BotAbTestRequest;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.BotAbTestResponse;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotSnapshotService;
import com.sankuai.gaigc.arrange.common.core.bot.utils.DtoConvert;
import com.sankuai.gaigc.arrange.common.core.monitor.AppExecutionRecorder;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.GrayPublishUtils;
import com.sankuai.gaigc.arrange.api.enums.ErrorCodeEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.ThriftUtils;
import com.sankuai.gaigc.arrange.common.enums.AppEntityTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 聊天助手执行接口
 * */
@Slf4j
@MdpThriftServer
public class BotRunServiceImpl implements BotRunService {
    @Autowired
    AIBotSnapshotService botSnapshotService;

    @Autowired
    AppExecutionRecorder appExecutionRecorder;

    @Autowired
    ArenaAbTestService arenaAbTestService;

    @Override
    public BotRunResponse runBot(BotRunRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getId()) || request.getId() <= 0) {
            return ThriftUtils.chatBotResponseFailure(ErrorCodeEnum.INPUT_INVALID, "参数缺失或id为Null或小于0", null);
        }

        BotRunParam runParam = new BotRunParam();
        runParam.setBotId(request.getId());
        runParam.setUserMsg(request.getUserMsg());
        runParam.setUserImgUrls(request.getUserImgUrls());
        runParam.setUser(DtoConvert.convert(request.getUser()));
        runParam.setStream(false);
        runParam.setDebug(Objects.nonNull(request.getDebug()) && request.getDebug());

        if (Objects.nonNull(request.getBizParams())) {
            Map<String, Object> bizParams = request.getBizParams().entrySet().stream().collect(HashMap::new, (hashMap, entry) -> hashMap.put(entry.getKey(), ThriftUtils.parseThriftParam(entry.getValue())), HashMap::putAll);
            runParam.setBizParams(bizParams);
        }

        runParam.setBizMsgHistory(DtoConvert.convertChatMsgList(request.getBizMsgHistory()));

        String sessionId = StringUtils.isBlank(request.getSessionId()) ? UUID.randomUUID().toString() : request.getSessionId();
        runParam.setSessionId(sessionId);
        AIBot bot = null;
        BotRunResult runResult = null;
        Exception exception = null;
        try {
            if (request.getVersion() == null) {
                //如果正在灰度发布并命中，则替换版本，否则仍为null
                request.setVersion(GrayPublishUtils.getGrayPublishVersion(AppEntityTypeEnum.BOT, request.getId()));
            }

            //执行abTest逻辑 若配置实验 查询分流结果 覆盖版本
            BotAbTestResponse abTestResponse = arenaAbTestService.executeBotAbTest(new BotAbTestRequest(request.getId(),request.getArenaAbRequest()));
            if (abTestResponse.isDoAbTest()) {
                request.setVersion(abTestResponse.getVersion());
            }

            bot = botSnapshotService.generateAIBot(runParam.getBotId(), request.getVersion());
            if (Objects.isNull(bot)) {
                return ThriftUtils.chatBotResponseFailure(ErrorCodeEnum.INPUT_INVALID, "聊天助手[" + runParam.getBotId() + "]不存在或无法加载", null);
            }
            // 执行Bot
            runResult = bot.processQueryByPlan(runParam);

            return DtoConvert.convertChatBotRunRes(runResult, runParam.getSessionId());
        } catch (Exception e) {
            log.error("聊天助手[" + runParam.getBotId() + "]执行异常", e);
            return ThriftUtils.chatBotResponseFailure(ErrorCodeEnum.INNER_ERROR, e.getMessage(), null);
        } finally {
            //记录日志
            appExecutionRecorder.recordBot(bot, FuxiCallSourceEnum.API, runResult, runParam, null, exception);
        }
    }
}
