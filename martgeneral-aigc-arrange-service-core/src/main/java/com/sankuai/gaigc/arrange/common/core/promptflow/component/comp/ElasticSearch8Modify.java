package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.meituan.poros.client.PorosApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.gaigc.arrange.common.core.promptflow.constants.ElasticSearchConstants.*;

@PromptFlowComponent(name = "ElasticSearch8Modify", desc = "ES8数据修改组件", type = ComponentTypeEnum.ELASTIC8_SEARCH_MODIFY, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "es8modifyType", desc = "修改类型(INSERT:插入,DELETE:删除)", type = ParamTypeEnum.STRING, category = CategoryEnum.COMMON_OPTION, required = true),
        @Param(name = "index", desc = "ES索引", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "keyIds", desc = "主键Ids(JSON-List<String>格式)" +
                "【INSERT: keyIds选填。如果不指定或者或者keyIds的数量不足时，系统将自动生成。" +
                "DELETE: keyIds必填，否则删除任务不执行。】", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "inputData", desc = "更新数据(JSON-List<Map>格式)" +
                "【INSERT: inputData必填，根据inputData数据数量相应插入ES数据库。" +
                "DELETE: inputData不填，删除任务仅需要主键Id。】", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = false),
})
@OutputParamDefinition({
        @Param(name = "affectedCount", desc = "本次修改影响数据数量", type = ParamTypeEnum.INT, category = CategoryEnum.DEFAULT, required = false)}
)
@Slf4j
public class ElasticSearch8Modify extends AbstractComponent {

    @Resource
    private PorosApiClient porosApiClient;

    @Override
    public Map<String, Object> execute(Context context, ComponentInfo componentInfo) throws Exception {
        String modifyType = ((String) parseParam("es8modifyType", componentInfo)).toUpperCase();
        String index = (String)parseParam("index", componentInfo);
        List inputData = (List)parseParam("inputData", componentInfo);
        List keyIds = (List)parseParam("keyIds", componentInfo);
        Map<String,Object> result = new HashMap<>();

        switch (modifyType) {
            case INSERT:
                result.put("affectedCount", insertData(index, keyIds, inputData));
                break;
            case DELETE:
                result.put("affectedCount", deleteData(index, keyIds));
                break;
        }

        return result;
    }



    private Long insertData(String index, List<String> keyIds, List<Map> inputDatas) throws IOException {
        BulkRequest.Builder bulkRequest = new BulkRequest.Builder();
        for (int i = 0; i < inputDatas.size(); i++) {
            int finalI = i;
            bulkRequest.operations(op ->
                    op.index(idx -> idx
                            .index(index)
                            .id(CollectionUtils.isNotEmpty(keyIds) && keyIds.size() > finalI ? keyIds.get(finalI) : null)
                            .document(inputDatas.get(finalI))
                    )
            );
        }
        BulkResponse bulkResponse = porosApiClient.bulk(bulkRequest.build());
        return bulkResponse.items().stream()
                .filter(response -> {
                    if (response.error() != null) {
                        // 打印log日志
                        log.error("Bulk request failed: {}", response.error().reason());
                        return false;
                    }
                    return true;
                }).count();
    }


    private Long deleteData(String index, List<String> keyIds) throws IOException {
        BulkRequest.Builder bulkRequest = new BulkRequest.Builder();
        if (CollectionUtils.isEmpty(keyIds)) {
            log.info("keyIds为空，删除索引无需执行");
            return 0L;
        }
        keyIds.stream().filter(keyId -> !StringUtils.isBlank(keyId)).forEach(
                keyId -> bulkRequest.operations(op -> op
                        .delete(del -> del
                                .index(index)
                                .id(keyId)
                        )
                )
        );
        BulkResponse bulkResponse = porosApiClient.bulk(bulkRequest.build());
        return bulkResponse.items().stream()
                .filter(response -> {
                    if (response.error() != null) {
                        log.error("Bulk request failed: {}", response.error().reason());
                        return false;
                    }
                    return true;
                }).count();
    }

}

