/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance;

import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Bot增强数据结构转化
 *
 * <AUTHOR>
 * @created 2024/8/21
 */
@Slf4j
public class BotEnhanceConverter {

    public static <T extends BotEnhanceItem> List<T> parseEnhanceConfig(String enhanceJson, Map<Long, String> idAndClassName) {
        List<T> list = Lists.newArrayList();
        if (StringUtils.isBlank(enhanceJson)) {
            return list;
        }
        List<BotEnhanceItem> botEnhanceItems = GsonUtil.fromJson(enhanceJson, new TypeToken<List<BotEnhanceItem>>() {
        }.getType());
        if (CollectionUtils.isEmpty(botEnhanceItems)) {
            return list;
        }
        List<Map<String, Object>> objList = GsonUtil.fromJson(enhanceJson, new TypeToken<List<Map<String, Object>>>() {
        }.getType());

        for (int i = 0; i < botEnhanceItems.size(); i++) {
            Map<String, Object> objectMap = objList.get(i);
            String simpleClassName = idAndClassName.get(botEnhanceItems.get(i).getId());
            if (StringUtils.isBlank(simpleClassName)) {
                continue;
            }

            T config = parseEnhance(objectMap, simpleClassName);
            if (Objects.isNull(config)) {
                continue;
            }
            config.setClassName(simpleClassName);
            list.add(config);
        }
        // 排序
        return list.stream().sorted(Comparator.comparing(BotEnhanceItem::getSort, Comparator.nullsLast(Integer::compareTo))).collect(Collectors.toList());
    }

    /**
     * 解析增强配置
     */
    public static <T extends BotEnhanceItem> T parseEnhance(Map<String, Object> objectMap, String simpleClassName) {
        if (StringUtils.isBlank(simpleClassName)) {
            return null;
        }

        try {
            String configClass = BotEnhanceItem.class.getName().replace(BotEnhanceItem.class.getSimpleName(), simpleClassName);
            Class<T> targetClz = getTargetClz(configClass);
            return targetClz.cast(GsonUtil.fromJson(GsonUtil.toJson(objectMap), targetClz));
        } catch (Exception e) {
            log.error("解析增强配置异常, objectMap:{}, e=", objectMap, e);
            return null;
        }
    }

    private static <T extends BotEnhanceItem> Class<T> getTargetClz(String configClass) {
        try {
            return (Class<T>) Class.forName(configClass);
        } catch (ClassNotFoundException e) {
            log.error("解析类型出错, 找不到指定类, configClass:{}, e=", configClass, e);
            throw new RuntimeException("解析类型出错, 找不到指定类, contextClzName:" + configClass, e);
        }
    }
}