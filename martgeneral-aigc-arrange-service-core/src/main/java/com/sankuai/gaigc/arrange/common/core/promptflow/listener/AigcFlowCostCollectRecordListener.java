package com.sankuai.gaigc.arrange.common.core.promptflow.listener;

import com.alibaba.fastjson.JSON;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mdp.boot.starter.mafka.consumer.AbstractMdpListener;
import com.meituan.mdp.boot.starter.mafka.consumer.anno.MdpMafkaMsgReceive;
import com.sankuai.gaigc.arrange.common.core.bot.service.AigcFlowCostCollectRecordService;
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.aigc.flow.cost.collect.AigcFlowCostCollect;
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.aigc.flow.cost.collect.AigcFlowCostCollectUsageCostSum;
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.aigc.flow.cost.collect.CalculateFlowCost;
import com.sankuai.gaigc.arrange.dao.dal.entity.AigcFlowCostCollectRecordDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @see <a href="https://km.sankuai.com/collabpage/1758236295">FRIDAY大模型应用平台接口调用计费说明</a>'
 * 张俊杰
 * 2024年05月27日18:59:35
 */
@Slf4j
@Service("aigcFlowCostCollectRecordListener")
public class AigcFlowCostCollectRecordListener {

    @Autowired
    private AigcFlowCostCollectRecordService aigcFlowCostCollectRecordService;

    @MdpMafkaMsgReceive(customSubscribeGroup = "aigc-flow-cost-collect-record-consumer")
    protected ConsumeStatus receive(List<String> msgs, List<AbstractMdpListener.MdpMqContext> contexts) {
        log.info(" 接收到消息 ：{}", JSON.toJSONString(msgs));
        log.info(" 接收到消息 ：{}", JSON.toJSONString(contexts));
        List<AigcFlowCostCollect> aigcFlowCostCollectList = msgs.stream()
                .map(AigcFlowCostCollect::convertByJsonStr)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(aigcFlowCostCollectList)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        MultiValueMap<AigcFlowCostCollect.AigcFlowCostCollectGroupKey, AigcFlowCostCollect> aigcFlowCostCollectGroupKeyMultiValueMap = new LinkedMultiValueMap<>();
        aigcFlowCostCollectList.forEach(aigcFlowCostCollect -> aigcFlowCostCollectGroupKeyMultiValueMap.add(aigcFlowCostCollect.toAigcFlowCostCollectGroupKey(), aigcFlowCostCollect));

        List<AigcFlowCostCollectRecordDO> aigcFlowCostCollectRecordDOList = new ArrayList<>();
        CalculateFlowCost calculateFlowCost = new CalculateFlowCost();
        aigcFlowCostCollectGroupKeyMultiValueMap.keySet()
                .forEach(aigcFlowCostCollectGroupKey -> {
                    AigcFlowCostCollectUsageCostSum aigcFlowCostCollectUsageCostSum = new AigcFlowCostCollectUsageCostSum(aigcFlowCostCollectGroupKey.getModelName(), calculateFlowCost)
                            .calculate(aigcFlowCostCollectGroupKeyMultiValueMap.get(aigcFlowCostCollectGroupKey), calculateFlowCost, aigcFlowCostCollectGroupKey.getModelName());
                    if (aigcFlowCostCollectUsageCostSum != null) {
                        aigcFlowCostCollectRecordDOList.add(aigcFlowCostCollectUsageCostSum.convertAigcFlowCostCollectRecordDO(aigcFlowCostCollectGroupKey));
                    }
                });

        // 将 aigcFlowCostCollectRecordDOList落库

        int effectRow = aigcFlowCostCollectRecordService.recordUsageAndCost(aigcFlowCostCollectRecordDOList);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
