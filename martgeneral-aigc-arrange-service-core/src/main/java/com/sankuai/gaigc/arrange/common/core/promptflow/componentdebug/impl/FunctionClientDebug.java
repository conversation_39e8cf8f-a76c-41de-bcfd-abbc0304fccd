package com.sankuai.gaigc.arrange.common.core.promptflow.componentdebug.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.sankuai.gaigc.arrange.common.core.promptflow.componentdebug.IFunctionClientDebug;
import com.sankuai.gaigc.arrange.common.core.promptflow.function.*;
import com.sankuai.gaigc.arrange.common.util.ApplicationContextUtil;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.dao.dal.dto.converter.function.AigcFunctionDtoConverter;
import com.sankuai.gaigc.arrange.dao.dal.dto.function.*;
import com.sankuai.gaigc.arrange.dao.dal.entity.AigcFunctionDO;
import com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO;
import com.sankuai.gaigc.arrange.dao.dal.enums.FunctionParamTypeEnum;
import com.sankuai.gaigc.arrange.dao.dal.enums.FunctionTypeEnum;
import com.sankuai.gaigc.arrange.dao.dal.example.aigcFunctionDraftDOExample;
import com.sankuai.gaigc.arrange.dao.dal.mapper.AigcFunctionDraftDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhangjiahui
 * @description:
 */
@Service
@Slf4j
public class FunctionClientDebug implements IFunctionClientDebug {

    @Resource
    private AigcFunctionDraftDOMapper aigcFunctionDraftDOMapper;

    @Resource
    private GenericHttpService genericHttpService;

    @Resource
    private GenericThriftService genericThriftService;
    @Resource
    private GenericPigeonService genericPigeonService;

    @Override
    public Map<String, Object> execute(Long functionId, Integer answerType, Map<String, Object> paramInputs, Map<String, Object> headerInputs,
                                       Integer timeout) {

        HashMap<String, Object> result = Maps.newHashMap();
        AigcFunctionDto functionDto = fundFunctionById(functionId);
        Map<String, String> headers = Objects.nonNull(headerInputs) ? headerInputs.entrySet().stream().collect(HashMap::new, (map, item) ->
                map.put(item.getKey(), Objects.nonNull(item.getValue()) ? String.valueOf(item.getValue()) : null), HashMap::putAll) : Maps.newHashMap();


        List<ParamValueInfo> params = assembleParams(functionDto.getId(), functionDto.getParamInfo().getParams(), paramInputs);
        List<ParamValueInfo> headersParam = assembleHeaders(functionDto.getParamInfo().getHeaders(), headers);
        FunctionClientParamInfo functionClientParamInfo = new FunctionClientParamInfo(params, headersParam);

        if (Objects.isNull(functionDto)) {
            return result;
        }
        try {
            Object functionResult = executeFunction(functionDto, functionClientParamInfo, timeout);
            result.put("response", answerType == 1 ? functionResult : GsonUtil.toJson(functionResult));
        } catch (Exception e) {
            log.error("error executing function. function:{}, params:{}", JSON.toJSONString(functionDto), JSON.toJSONString(paramInputs), e);
        }
        return result;
    }

    private List<ParamValueInfo> assembleParams(Long functionId, List<ParamDefineModel> paramList, Map<String, Object> paramInputs) {
        List<ParamValueInfo> valueInfos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(paramList)) {
            return valueInfos;
        }
        for (ParamDefineModel param : paramList) {
            String identity = param.getIdentity();
            valueInfos.add(parseParamValue(functionId, param, paramInputs.get(identity)));
        }

        return valueInfos;
    }

    private List<ParamValueInfo> assembleHeaders(List<ParamDefineModel> paramList, Map<String, String> headers){
        List<ParamValueInfo> valueInfos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(paramList)) {
            return valueInfos;
        }
        return paramList.stream().map(param -> {
            param.setDataType(FunctionParamTypeEnum.STRING.getValue());

            ParamValueInfo valueInfo = new ParamValueInfo();
            valueInfo.setIdentity(param.getIdentity());
            valueInfo.setParamType(FunctionParamTypeEnum.STRING);
            valueInfo.setParamClass(param.getParamClass());
            valueInfo.setParamValue(parseBasicTypeParam(param, headers.get(param.getIdentity())));
            return valueInfo;
        }).collect(Collectors.toList());
    }

    private ParamValueInfo parseParamValue(Long functionId, ParamDefineModel param, Object paramValue) {
        FunctionParamTypeEnum paramTypeEnum = FunctionParamTypeEnum.getEnumByValue(param.getDataType());
        ParamValueInfo valueInfo = new ParamValueInfo();
        valueInfo.setIdentity(param.getIdentity());
        valueInfo.setParamType(paramTypeEnum);
        valueInfo.setParamClass(param.getParamClass());
        if (StringUtils.isBlank(valueInfo.getParamClass())) {
            valueInfo.setParamClass(valueInfo.getParamType().getJavaClassName());
            if (valueInfo.getParamType().isRefType()) {
                log.warn("The reference type parameter did not provide a parameter type, functionId:{}", functionId);
            }
        }

        if (paramTypeEnum.isCollectionType()) {
            valueInfo.setParamValue(parseArrayParam(param, paramValue));
            return valueInfo;
        }
        if (FunctionParamTypeEnum.OBJECT == paramTypeEnum) {
            valueInfo.setParamValue(parseObjectParam(param, paramValue));
            return valueInfo;
        }
        if (FunctionParamTypeEnum.MAP == paramTypeEnum) {
            if (paramValue instanceof  Map) {
                valueInfo.setParamValue(paramValue);
            } else {
                if (null != paramValue) {
                    valueInfo.setParamValue(GsonUtil.fromJson(paramValue.toString(), new TypeToken<Map<String, Object>>() {
                    }.getType()));
                } else {
                    log.warn("paramValue is null ");
                }

            }
            return valueInfo;
        }
        valueInfo.setParamValue(parseBasicTypeParam(param, paramValue));
        return valueInfo;
    }

    private Object parseArrayParam(ParamDefineModel param, Object modelRecognitionValue) {
        if (Objects.isNull(modelRecognitionValue)) {
            return null;
        }
        ParamDefineModel arrayElement = param.getArrayElement();
        Preconditions.checkArgument(Objects.nonNull(arrayElement), "数组元素类型未定义，参数名：" + param.getName());
        FunctionParamTypeEnum paramTypeEnum = FunctionParamTypeEnum.getEnumByValue(arrayElement.getDataType());
        if (FunctionParamTypeEnum.LIST == paramTypeEnum) {
            List<Object> result = GsonUtil.fromJson(GsonUtil.toJson(modelRecognitionValue), new TypeToken<List<Object>>() {
            }.getType());
            return result.stream().filter(Objects::nonNull).map(o -> parseObjectParam(arrayElement, o)).collect(Collectors.toList());
        }
        if (modelRecognitionValue instanceof List) {
            //对list里面的元素进行类型转换,并且返回list
            return ((List<Object>) modelRecognitionValue).stream()
                    .map(item -> parseBasicTypeParam(arrayElement, item)).collect(Collectors.toList());
        }
        if (modelRecognitionValue instanceof Set) {
            //对set里面的元素进行类型转换,并且返回set
            return ((Set<Object>) modelRecognitionValue).stream()
                    .map(item -> parseBasicTypeParam(arrayElement, item)).collect(Collectors.toSet());
        }
        return modelRecognitionValue;
    }

    private Map<String, Object> parseObjectParam(ParamDefineModel param, Object modelRecognitionValue) {
        Map<String, Object> result = Maps.newHashMap();
        if (Objects.isNull(modelRecognitionValue)) {
            return result;
        }

        //解析参数值，支持传入map和object(json)
        Map<String, Object> objectMap;
        if (modelRecognitionValue instanceof Map){
            objectMap = (Map<String, Object>) modelRecognitionValue;
        }else {
            objectMap = GsonUtil.fromJson((String) modelRecognitionValue, new TypeToken<Map<String, Object>>() {}.getType());
        }
        for (ParamDefineModel objAttribute : param.getObjAttributes()) {
            Object value = objectMap.get(objAttribute.getIdentity());
            FunctionParamTypeEnum paramTypeEnum = FunctionParamTypeEnum.getEnumByValue(objAttribute.getDataType());
            if (FunctionParamTypeEnum.OBJECT == paramTypeEnum) {
                result.put(objAttribute.getIdentity(), parseObjectParam(objAttribute, value));
                continue;
            }
            if (paramTypeEnum.isCollectionType()) {
                result.put(objAttribute.getIdentity(), parseArrayParam(objAttribute, value));
                continue;
            }
            result.put(objAttribute.getIdentity(), parseBasicTypeParam(objAttribute, value));
        }
        return result;
    }

    public static Object parseBasicTypeParam(ParamDefineModel param, Object value) {
        //默认值
        if (Objects.isNull(value) || StringUtils.isBlank(value.toString())) {
             value = param.getDefaultValue();
        }

        if(Objects.isNull(value) || StringUtils.isBlank(value.toString())){
            return null;
        }

        FunctionParamTypeEnum paramTypeEnum = FunctionParamTypeEnum.getEnumByValue(param.getDataType());
        switch (paramTypeEnum) {
            case STRING:
                return String.valueOf(value);
            case INT:
                return Integer.parseInt(value.toString());
            case LONG:
                return Long.parseLong(value.toString());
            case DOUBLE:
                return Double.parseDouble(value.toString());
            case FLOAT:
                return Float.parseFloat(value.toString());
            case BOOLEAN:
                return Boolean.parseBoolean(value.toString());
            default:
                return value;
        }
    }

    public AigcFunctionDto fundFunctionById(Long functionId) {
        aigcFunctionDraftDOExample aigcFlowSnapshotExample = new aigcFunctionDraftDOExample();
        aigcFlowSnapshotExample.createCriteria().andFunctionIdEqualTo(functionId);
        List<aigcFunctionDraftDO> aigcFunctionDraftDOS = aigcFunctionDraftDOMapper.selectByExampleWithBLOBs(aigcFlowSnapshotExample);
        if (Objects.isNull(aigcFunctionDraftDOS) || aigcFunctionDraftDOS.isEmpty()) {
            return null;
        }
        AigcFunctionDO aigcFunctionDO = convertAigcFunctionDO(aigcFunctionDraftDOS.get(0));
        return AigcFunctionDtoConverter.convertToAigcFunctionDto(aigcFunctionDO);
    }

    private AigcFunctionDO convertAigcFunctionDO(aigcFunctionDraftDO aigcFunctionDraftDO) {
        AigcFunctionDO aigcFunctionDO = new AigcFunctionDO();
        aigcFunctionDO.setId(aigcFunctionDraftDO.getFunctionId());
        aigcFunctionDO.setName(aigcFunctionDraftDO.getName());
        aigcFunctionDO.setCnName(aigcFunctionDraftDO.getCnName());
        aigcFunctionDO.setDescription(aigcFunctionDraftDO.getDescription());
        aigcFunctionDO.setFuncType(aigcFunctionDraftDO.getFuncType());
        aigcFunctionDO.setCallInfo(aigcFunctionDraftDO.getCallInfo());
        aigcFunctionDO.setParamInfo(aigcFunctionDraftDO.getParamInfo());
        aigcFunctionDO.setCreator(aigcFunctionDraftDO.getCreator());
        aigcFunctionDO.setModifier(aigcFunctionDraftDO.getModifier());
        aigcFunctionDO.setCreateTime(aigcFunctionDraftDO.getCreateTime());
        aigcFunctionDO.setUpdateTime(aigcFunctionDraftDO.getUpdateTime());
        aigcFunctionDO.setFollowUpInfo(aigcFunctionDraftDO.getFollowUpInfo());
        aigcFunctionDO.setExt(aigcFunctionDraftDO.getExt());
        return aigcFunctionDO;

    }

    public Object executeFunction(AigcFunctionDto functionDto, FunctionClientParamInfo functionClientParamInfo, Integer timeout) throws Exception {
        try {
            FunctionTypeEnum functionType = functionDto.getFuncType();
            if (FunctionTypeEnum.HTTP == functionType) {
                return executeHttpFunctionThrowsException(functionDto, functionClientParamInfo, timeout);
            } else if (FunctionTypeEnum.MTTHRIFT == functionType) {
                return executeThriftFunctionThrowsException(functionDto, functionClientParamInfo, timeout);
            } else if (FunctionTypeEnum.PIGEON == functionType) {
                return executePigeonFunctionThrowsException(functionDto, functionClientParamInfo, timeout);
            } else if (FunctionTypeEnum.LOCAL_METHOD == functionType || FunctionTypeEnum.WORK_FLOW == functionType) {
                return executeLocalMethodFunctionThrowsException(functionDto, functionClientParamInfo, timeout);
            }
            throw new RuntimeException(String.format("unrecognized function type. function:%s, paramInfo:%s" , functionDto, functionClientParamInfo));
        } catch (Exception e) {
            log.error("executeFunction error, function = {}, e = ", functionDto, e);
            if (e instanceof InvocationTargetException){
                throw (Exception) ((InvocationTargetException) e).getTargetException();
            }else {
                throw e;
            }
        }
    }

    private Object executeLocalMethodFunctionThrowsException(AigcFunctionDto function, FunctionClientParamInfo functionClientParamInfo, Integer timeout) throws Exception {
        LocalMethodFunctionCallInfo localMethodFunctionCallInfo = (LocalMethodFunctionCallInfo) function.getCallInfo();

        String serviceName = localMethodFunctionCallInfo.getServiceName();
        String methodName = localMethodFunctionCallInfo.getMethodName();
        Boolean isSpringBean = localMethodFunctionCallInfo.getIsSpringBean();

        Class<?> serviceClass = Class.forName(serviceName);
        Object instance = isSpringBean ? ApplicationContextUtil.getBean(serviceClass) : serviceClass.newInstance();
        List<ParamValueInfo> params = functionClientParamInfo.getParams();
        Class[] paramClassArr = new Class[params.size()];
        for (int i = 0; i < params.size(); ++i) {
            paramClassArr[i] = Class.forName(params.get(i).getParamClass());
        }
        Method method = serviceClass.getMethod(methodName, paramClassArr);
        Object[] paramValue = functionClientParamInfo.getParams().stream().map(ParamValueInfo::getParamValue).toArray();
        Object response = method.invoke(instance, paramValue);

        return response;
    }

    private String executePigeonFunctionThrowsException(AigcFunctionDto function, FunctionClientParamInfo functionClientParamInfo, Integer timeout) throws Exception {
        PigeonFunctionCallInfo pigeonFunctionCallInfo = (PigeonFunctionCallInfo) function.getCallInfo();

        PigeonParam pigeonParam = new PigeonParam();
        pigeonParam.setAppKey(pigeonFunctionCallInfo.getPigeonAppkey());
        pigeonParam.setServiceName(pigeonFunctionCallInfo.getPigeonServiceName());
        pigeonParam.setMethodName(pigeonFunctionCallInfo.getPigeonMethodName());
        pigeonParam.setTimeout(getFunctionCallTimeout(function, timeout));
        pigeonParam.setGeneric("json-simple");
        Optional.ofNullable(functionClientParamInfo.getParams()).ifPresent(paramInput -> {
            pigeonParam.setParamType(functionClientParamInfo.getParams().stream().map(ParamValueInfo::getParamClass).collect(Collectors.toList()));
            pigeonParam.setParamValue(functionClientParamInfo.getParams().stream().map(ParamValueInfo::getParamValue).collect(Collectors.toList()));
        });

        return genericPigeonService.invokeGenericPigeonService(pigeonParam);
    }

    private String executeThriftFunctionThrowsException(AigcFunctionDto function, FunctionClientParamInfo functionClientParamInfo, Integer timeout) throws Exception {
        MtThriftFunctionCallInfo thriftFunctionCallInfo = (MtThriftFunctionCallInfo) function.getCallInfo();

        ThriftParam thriftParam = new ThriftParam();
        thriftParam.setAppKey(thriftFunctionCallInfo.getThriftRemoteAppKey());
        thriftParam.setGenericServiceName(thriftFunctionCallInfo.getThriftServiceName());
        thriftParam.setMethodName(thriftFunctionCallInfo.getThriftMethodName());
        thriftParam.setTimeout(getFunctionCallTimeout(function, timeout));
        thriftParam.setPort(thriftFunctionCallInfo.getThriftPort());
        thriftParam.setGeneric("json-simple");
        Optional.ofNullable(functionClientParamInfo.getParams()).ifPresent(paramInput -> {
            thriftParam.setParamType(functionClientParamInfo.getParams().stream().map(ParamValueInfo::getParamClass).collect(Collectors.toList()));
            thriftParam.setParamValue(functionClientParamInfo.getParams().stream().map(ParamValueInfo::getParamValue).collect(Collectors.toList()));
        });

        return genericThriftService.invokeGenericThriftService(thriftParam);
    }

    private String executeHttpFunctionThrowsException(AigcFunctionDto function, FunctionClientParamInfo functionClientParamInfo, Integer timeout) throws Exception {
        HttpFunctionCallInfo httpFunctionCallInfo = (HttpFunctionCallInfo) function.getCallInfo();
        Map<String, String> headers = Objects.nonNull(functionClientParamInfo.getHeaders()) ? functionClientParamInfo.getHeaders().stream().collect(HashMap::new, (map, item) -> map.put(item.getIdentity(),
                Objects.nonNull(item.getParamValue()) ? String.valueOf(item.getParamValue()) : null), HashMap::putAll) : Maps.newHashMap();

        HttpParam httpParam = new HttpParam();
        httpParam.setHeaders(headers);
        httpParam.setTimeout(getFunctionCallTimeout(function, timeout));
        httpParam.setUrl(httpFunctionCallInfo.getHttpUrl());
        httpParam.setMethod(httpFunctionCallInfo.getHttpMethod());
        Optional.ofNullable(functionClientParamInfo.getParams()).ifPresent(paramInput -> {
            httpParam.setParamName(functionClientParamInfo.getParams().stream().map(ParamValueInfo::getIdentity).collect(Collectors.toList()));
            httpParam.setParamValue(functionClientParamInfo.getParams().stream().map(ParamValueInfo::getParamValue).collect(Collectors.toList()));
        });

        return genericHttpService.invokeHttpService(httpParam);
    }

    private Integer getFunctionCallTimeout(AigcFunctionDto function , Integer timeout){
        if (timeout != null && timeout > 0){
            return timeout;
        }

        if (function.getCallInfo() != null && function.getCallInfo().getTimeout() != null && function.getCallInfo().getTimeout() > 0){
            return function.getCallInfo().getTimeout();
        }

        return 120000;
    }
}
