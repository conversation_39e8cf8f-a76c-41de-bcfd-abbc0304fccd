/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.enhance.start;

import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.BotEnhanceItem;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotEnhanceParallelResult;
import com.sankuai.gaigc.arrange.common.core.bot.enhance.BotStartEnhanceHandler;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotEnhanceRunMode;
import org.springframework.stereotype.Component;

/**
 * 空的处理器
 *
 * <AUTHOR>
 * @created 2024/8/16
 */
@Component
public class NothingStartHandler implements BotStartEnhanceHandler {

    @Override
    public void handle(BotEnhanceItem enhanceConfig, BotRunParam runParam, AIBot bot) {

    }

    @Override
    public String relateConfigClassName() {
        return BotEnhanceItem.class.getSimpleName();
    }

    @Override
    public BotEnhanceParallelResult parallelHandle(BotEnhanceItem enhanceConfig, BotRunParam runParam, AIBot bot) {
        return null;
    }

    @Override
    public void afterParallelHandle(BotEnhanceParallelResult result, BotRunParam runParam, boolean isLastOne) {
    }

    @Override
    public BotEnhanceRunMode mode() {
        return BotEnhanceRunMode.SERIAL;
    }
}