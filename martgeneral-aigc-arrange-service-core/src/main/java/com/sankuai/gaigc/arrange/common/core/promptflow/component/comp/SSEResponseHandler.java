package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.meituan.mdp.langmodel.api.model.StreamResponseHandler;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.model.SSEMessage;
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils;
import com.sankuai.gaigc.arrange.common.core.friday.StreamAccumulateMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class SSEResponseHandler implements StreamResponseHandler<StreamAccumulateMessage> {
    private final SseEmitter emitter;

    private Long startTime;

    /**
     * 指标标签——
     */
    private String flowId;

    private String botId;

    private Long botStartTime;

    private String sectionId;

    public SSEResponseHandler(SseEmitter emitter) {
        this.emitter = emitter;
        this.startTime = System.currentTimeMillis();
        this.sectionId = SSEMessageUtils.generateSectionId();
    }

    public SSEResponseHandler(SseEmitter emitter, String flowId) {
        this.emitter = emitter;
        this.flowId = flowId;
        this.startTime = System.currentTimeMillis();
        this.sectionId = SSEMessageUtils.generateSectionId();
    }

    public SSEResponseHandler(SseEmitter emitter, String flowId, String botId, Long botStartTime, String sectionId) {
        this.emitter = emitter;
        this.flowId = flowId;
        this.startTime = System.currentTimeMillis();
        this.botId = botId;
        this.botStartTime = botStartTime;
        this.sectionId = sectionId;
    }

    @Override
    public void onNext(StreamAccumulateMessage chunk) {
        try {
            SSEMessage message = SSEMessageUtils.generateAnswerMessage(chunk, this.sectionId);
            if(!message.getIs_finish()){
                //没有结束，判断首token出现时间。首token出现时content和累积content相同
                if(message.getContent().equals(message.getAccumulate_content())){
                    SSEMessageUtils.logMetric(startTime,flowId,"FIRST_TOKEN");
                    //bot执行LLM场景下按botId进行首token埋点
                    if (Objects.nonNull(botId) && Objects.nonNull(botStartTime) && Objects.nonNull(message.getIndex()) &&
                            message.getIndex().equals(0)) {
                        Cat.newCompletedTransactionWithDuration("BotFirstToken", "bot_" + Optional.ofNullable(botId).orElse("default"), System.currentTimeMillis() - botStartTime);

                        if(StreamInfoHolder.get() != null){
                            StreamInfoHolder.get().setFirstTokenTime(System.currentTimeMillis());
                        }
                    }
                }
            }
            emitter.send(message);
        } catch (IOException e) {
            log.error("sse response handler error for chunk = {}",chunk,e);
            onError(e);
        }
    }



    @Override
    public void onError(Throwable throwable) {
        //emitter.completeWithError(throwable);
    }

    @Override
    public void onComplete() {
        //emitter.complete();
    }
}