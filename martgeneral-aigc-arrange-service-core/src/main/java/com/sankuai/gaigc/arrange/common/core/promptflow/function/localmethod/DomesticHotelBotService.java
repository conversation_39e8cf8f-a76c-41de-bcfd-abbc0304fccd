package com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.gaigc.arrange.api.entity.ArenaAbRequest;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.UserModel;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotExecuteService;
import com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod.entity.DomesticHotelUserParams;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.config.MccConfig;
import com.sankuai.service.wpt.horizon.core.enums.IdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * 酒店在线问问BOT
 * */
@Slf4j
@Service
public class DomesticHotelBotService {
    public static final String USER_ID = "userId";

    public static final String USER_INPUT = "userInput";
    public static final String USER_PARAM_STRING = "userParamString";

    public static final String DEFAULT = "DEFAULT";
    @Autowired
    private AIBotExecuteService botExecuteService;

    private static ExecutorService STREAM_BOT_POOL = TraceExecutors.getTraceExecutorService(
            Rhino.newThreadPool("demestic_hotel_run_pool",
                    DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(10).withMaxQueueSize(100)
            ).getExecutor());

    @Autowired
    private MccConfig mccConfig;

    public Map<String, Object> runDomesticHotelBot(String appParamString, String poiName, List<Map<String, Object>> historyMessage,
                                      String goodsParamString, String poiId, String userInput, String userParamString, String userId, String token) {
        BotRunParam runParam = new BotRunParam();
        runParam.setBotId(mccConfig.getDemesticHotelBotId());
        Map<String, Object> bizParams = new HashMap<>();
        bizParams.put("appParamString", appParamString);
        bizParams.put("poiName", poiName);
        bizParams.put("historyMessage", historyMessage);
        bizParams.put("goodsParamString", goodsParamString);
        bizParams.put("poiId", poiId);
        bizParams.put("userInput", userInput);
        bizParams.put("userParamString", userParamString);
        bizParams.put("token", token);
        bizParams.put("userId", userId);
        runParam.setBizParams(bizParams);

        runParam.setDebug(false);
        runParam.setStream(false);
        runParam.setUser(new UserModel(Long.parseLong(userId), DEFAULT + userId, 1));
        runParam.setUserMsg(userInput);
        runParam.setSessionId(UUID.randomUUID().toString());

        CompletableFuture<Map<String, Object>> mapCompletableFuture = CompletableFuture.supplyAsync(() -> {
            try {
                Map<String, Object> map = botExecuteService.runBot(runParam);
                return map;
            } catch (Exception e) {
                log.error("runDomesticHotelBot error!", e);
                return Collections.emptyMap();
            }
        }, STREAM_BOT_POOL);
        return mapCompletableFuture.join();
    }

    public Map<String, Object> runDomesticHotelBotV2(Map<String,Object> bizParamsInput) {
        BotRunParam runParam = new BotRunParam();
        runParam.setBotId(mccConfig.getDemesticHotelBotId());
        runParam.setBizParams(bizParamsInput);
        String userId = (String)bizParamsInput.get(USER_ID);
        String userInput = (String) bizParamsInput.get(USER_INPUT);

        runParam.setDebug(false);
        runParam.setStream(false);
        runParam.setUser(new UserModel(Long.parseLong(userId), DEFAULT + userId, 1));
        runParam.setUserMsg(userInput);
        runParam.setSessionId(UUID.randomUUID().toString());

        CompletableFuture<Map<String, Object>> mapCompletableFuture = CompletableFuture.supplyAsync(() -> {
            try {
                Map<String, Object> map = botExecuteService.runBot(runParam);
                return map;
            } catch (Exception e) {
                log.error("runDomesticHotelBot error!", e);
                return Collections.emptyMap();
            }
        }, STREAM_BOT_POOL);
        return mapCompletableFuture.join();
    }

    public Map<String, Object> runDomesticHotelBot(Map<String,Object> bizParamsInput) {
        BotRunParam runParam = new BotRunParam();
        runParam.setBotId(mccConfig.getDemesticHotelBotId());
        runParam.setBizParams(bizParamsInput);
        String userId = (String)bizParamsInput.get(USER_ID);
        String userInput = (String) bizParamsInput.get(USER_INPUT);
        try {
            if (bizParamsInput.containsKey(USER_PARAM_STRING) && Objects.nonNull(bizParamsInput.get(USER_PARAM_STRING))) {
                ArenaAbRequest arenaAbRequest = new ArenaAbRequest();
                DomesticHotelUserParams domesticHotelUserParams = GsonUtil.fromJson((String) bizParamsInput.get(USER_PARAM_STRING), DomesticHotelUserParams.class);
                arenaAbRequest.setIdentification(domesticHotelUserParams.getUuid());
                arenaAbRequest.setIdType(IdTypeEnum.UUID.getCode());
                runParam.setArenaAbRequest(arenaAbRequest);
            }
        } catch (Exception e) {
            log.error("runDomesticHotelBot abtest error!", e);
        }

        runParam.setDebug(false);
        runParam.setStream(false);
        runParam.setUser(new UserModel(Long.parseLong(userId), "DEFAULT" + userId, 1));
        runParam.setUserMsg(userInput);
        runParam.setSessionId(UUID.randomUUID().toString());

        CompletableFuture<Map<String, Object>> mapCompletableFuture = CompletableFuture.supplyAsync(() -> {
            try {
                Map<String, Object> map = botExecuteService.runBot(runParam);
                return map;
            } catch (Exception e) {
                log.error("runDomesticHotelBot error!", e);
                return Collections.emptyMap();
            }
        }, STREAM_BOT_POOL);
        return mapCompletableFuture.join();
    }
}
