package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.nibhtp.os.htp.cms.access.common.search.Expression;
import com.meituan.nibhtp.os.htp.cms.access.common.search.Expressions;
import com.meituan.nibhtp.os.htp.cms.access.common.search.SearchQueryBuilder;
import com.meituan.nibhtp.os.htp.cms.access.thrift.req.DirectVectorSearchReq;
import com.meituan.nibhtp.os.htp.cms.access.thrift.req.QueryVectorSearchReq;
import com.meituan.nibhtp.os.htp.cms.access.thrift.resp.serch.VectorSearchResp;
import com.meituan.nibhtp.os.htp.cms.access.thrift.service.common.JsonContentThriftService;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.remote.basicapi.ContentEmbeddingRemoteService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

@PromptFlowComponent(name = "EmbeddingDBBatchQuery", desc = "向量库批量查询组件", type = ComponentTypeEnum.EMBEDDING_DB_BATCH_SEARCH_QUERY, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "contentTypeId", desc = "内容唯一标志", type = ParamTypeEnum.INT, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "vectorField", desc = "向量字段名", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "scalarSearchParam", desc = "标量检索相关参数", type = ParamTypeEnum.MAP, category = CategoryEnum.CUSTOM, required = false),
        @Param(name = "topK", desc = "向量召回topK", type = ParamTypeEnum.INT, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "subscribedFields", desc = "期望召回的索引字段，不传默认全部", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "queryList", desc = "query查询字段列表", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "numCandidates", desc = "numCandidates", type = ParamTypeEnum.INT, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "trackTotalHits", desc = "是否查询总命中数", type = ParamTypeEnum.BOOLEAN, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "readParent", desc = "检索后返回父内容数据，默认读当前内容", type = ParamTypeEnum.BOOLEAN, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "modelName", desc = "embedding模型，不传默认向量库做embedding", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false),
})
@OutputParamDefinition({
        @Param(name = "result", desc = "查询命中结果", type = ParamTypeEnum.JSON, category = CategoryEnum.DEFAULT, required = false)}
)
@Slf4j
public class EmbeddingDBBatchQuery extends AbstractComponent {
    //因多处有使用统一在ThriftClientConfig进行了定义
    @Autowired
    private JsonContentThriftService jsonContentThriftService;
    @Resource
    private ContentEmbeddingRemoteService contentEmbeddingRemoteService;

    @Override
    public Map<String, Object> execute(Context context, ComponentInfo componentInfo) throws Exception {
        //取出inputs
        Integer contentTypeId = (Integer) parseParam("contentTypeId", componentInfo);
        String vectorField = (String) parseParam("vectorField", componentInfo);
        Map<String, Object> scalarSearchParam = (Map<String, Object>)parseParam("scalarSearchParam", componentInfo);
        Integer topK = (Integer) parseParam("topK", componentInfo);
        List<Object> subscribedFields = parseListValues("subscribedFields", componentInfo);
        List<Object> queryList = parseListValues("queryList", componentInfo);
        Integer numCandidates = (Integer) parseParam("numCandidates", componentInfo);
        Boolean trackTotalHits = (Boolean) parseParam("trackTotalHits", componentInfo);
        Boolean readParent = (Boolean) parseParam("readParent", componentInfo);
        String modelName = (String) parseParam("modelName", componentInfo);

        Map<String, Object> allQueryResult = new HashMap<>();
        queryList.forEach(query -> {
            try {
                Map<String, Object> queryRecallResult;
                if (StringUtils.isBlank(modelName)) {
                    queryRecallResult = getEmbeddingRecallByQuery(Long.valueOf(contentTypeId), vectorField, scalarSearchParam, topK, subscribedFields,
                            String.valueOf(query), numCandidates, trackTotalHits, readParent);
                } else {
                    List<Float> queryEmbedding;
                    try {
                        queryEmbedding = getQueryEmbedding(modelName, String.valueOf(query));
                    } catch (Exception e) {
                        log.error("getQueryEmbedding error! query = " + query + "modelName = " + modelName, e);
                        throw new RuntimeException(e);
                    }
                    queryRecallResult = getEmbeddingRecallByEmbedding(Long.valueOf(contentTypeId), vectorField, scalarSearchParam, topK, subscribedFields,
                            queryEmbedding, numCandidates, trackTotalHits, readParent);
                }
                allQueryResult.put(String.valueOf(query), queryRecallResult);
            } catch (Exception e) {
                log.error("EmbeddingDBBatchQuery error! query = " + query, e);
                throw new RuntimeException(e);
            }
        });
        Map<String, Object> result = new HashMap<>();
        result.put("result", allQueryResult);
        return result;
    }

    private Map<String, Object> getEmbeddingRecallByQuery(Long contentTypeId, String vectorField, Map<String, Object> scalarSearchParam, Integer topK, List<Object> subscribedFields,
                                                          String query, Integer numCandidates, Boolean trackTotalHits, Boolean readParent) throws Exception {
        QueryVectorSearchReq queryVectorSearchReq = new QueryVectorSearchReq();
        queryVectorSearchReq.setQuery(query);
        queryVectorSearchReq.setContentTypeId(contentTypeId);
        queryVectorSearchReq.setVectorField(vectorField);
        Optional.ofNullable(subscribedFields).ifPresent(fields -> {
            queryVectorSearchReq.setSubscribedFields(fields.stream().map(String::valueOf).collect(Collectors.toSet()));
        });
        Optional.ofNullable(numCandidates).ifPresent(queryVectorSearchReq::setNumCandidates);
        Optional.ofNullable(trackTotalHits).ifPresent(queryVectorSearchReq::setTrackTotalHits);
        Optional.ofNullable(readParent).ifPresent(queryVectorSearchReq::setReadParent);
        queryVectorSearchReq.setScalarSearchParam(spliceScalarFilters(scalarSearchParam, topK));
        VectorSearchResp vectorSearchResp = jsonContentThriftService.vectorSearchWithQuery(queryVectorSearchReq);
        String vectorSearchRespJson = Optional.ofNullable(vectorSearchResp).map(GsonUtil::toJson).orElse("{}");
        return GsonUtil.fromJson(vectorSearchRespJson, Map.class);
    }

    private Map<String, Object> getEmbeddingRecallByEmbedding(Long contentTypeId, String vectorField, Map<String, Object> scalarSearchParam, Integer topK, List<Object> subscribedFields,
                                                              List<Float> queryEmbedding, Integer numCandidates, Boolean trackTotalHits, Boolean readParent) throws TException {
        DirectVectorSearchReq directVectorSearchReq = new DirectVectorSearchReq();
        directVectorSearchReq.setVector(queryEmbedding);
        directVectorSearchReq.setContentTypeId(contentTypeId);
        directVectorSearchReq.setVectorField(vectorField);
        Optional.ofNullable(subscribedFields).ifPresent(fields -> {
            directVectorSearchReq.setSubscribedFields(fields.stream().map(String::valueOf).collect(Collectors.toSet()));
        });
        Optional.ofNullable(numCandidates).ifPresent(directVectorSearchReq::setNumCandidates);
        Optional.ofNullable(trackTotalHits).ifPresent(directVectorSearchReq::setTrackTotalHits);
        Optional.ofNullable(readParent).ifPresent(directVectorSearchReq::setReadParent);
        directVectorSearchReq.setScalarSearchParam(spliceScalarFilters(scalarSearchParam, topK));
        VectorSearchResp vectorSearchResp = jsonContentThriftService.directVectorSearch(directVectorSearchReq);
        String vectorSearchRespJson = Optional.ofNullable(vectorSearchResp).map(GsonUtil::toJson).orElse("{}");
        return GsonUtil.fromJson(vectorSearchRespJson, Map.class);
    }


    private Map<String, String> spliceScalarFilters(Map<String, Object> scalarSearchParamMap, Integer topK) {
        SearchQueryBuilder searchQueryBuilder = new SearchQueryBuilder();
        searchQueryBuilder.limitOffset(topK, 0);
        if (scalarSearchParamMap == null || scalarSearchParamMap.isEmpty()) {
            return searchQueryBuilder.build();
        }
        List<Expression> expressionList = scalarSearchParamMap.entrySet().stream()
                .map(entry -> {
                    String value = String.valueOf(entry.getValue());
                    boolean isLikelyJsonArray = value.trim().matches("^\\[.*\\]$");
                    if (isLikelyJsonArray) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        try {
                            List<String> valueList = objectMapper.readValue(value, new TypeReference<List<String>>() {});
                            return Expressions.terms(entry.getKey(), valueList);
                        } catch (Exception e) {
                            log.error("spliceScalarFilters convert array error !", e);
                            throw new RuntimeException(e);
                        }
                    } else {
                        return Expressions.term(entry.getKey(), String.valueOf(entry.getValue()));
                    }
                }).collect(Collectors.toList());

        Expression expression = Expressions.and(expressionList);
        searchQueryBuilder.expression(expression);
        return searchQueryBuilder.build();
    }

    private List<Float> getQueryEmbedding(String modelName, String query) throws Exception {
        if (StringUtils.isBlank(modelName)) {
            throw new UnsupportedOperationException("不支持的Embedding模型");
        }
        return contentEmbeddingRemoteService.textEmbedding(query, modelName);
    }

}