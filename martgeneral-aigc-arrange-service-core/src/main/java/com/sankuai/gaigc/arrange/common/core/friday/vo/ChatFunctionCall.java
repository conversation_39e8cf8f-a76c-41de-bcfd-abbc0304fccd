package com.sankuai.gaigc.arrange.common.core.friday.vo;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ChatFunctionCall
 * <AUTHOR>
 * @Date 2025/2/19 下午3:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChatFunctionCall {

    /**
     * The name of the function being called
     */
    String name;

    /**
     * The arguments of the call produced by the model, represented as a JsonNode for easy manipulation.
     */
    JsonNode arguments;

}
