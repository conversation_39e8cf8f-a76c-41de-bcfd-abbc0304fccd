package com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod.Request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @created: 2024/7/8
 * @description:
 */
@Data
public class HotelRoomRecommendGoodsParam {
    @FieldDoc(
            description = ""
    )
    private Long poiId;
    @FieldDoc(
            description = ""
    )
    private Long checkInDate;
    @FieldDoc(
            description = ""
    )
    private Long checkOutDate;
    @FieldDoc(
            description = ""
    )
    private int numberOfAdults;
    @FieldDoc(
            description = ""
    )
    private int numberOfChildren;
    @FieldDoc(
            description = "儿童年龄，用英文逗号分隔"
    )
    private String childAges;
    @FieldDoc(
            description = "间数"
    )
    private int roomCount;
    @FieldDoc(
            description = ""
    )
    private Integer sellChannel;
    @FieldDoc(
            description = ""
    )
    private Integer goodsType;
}
