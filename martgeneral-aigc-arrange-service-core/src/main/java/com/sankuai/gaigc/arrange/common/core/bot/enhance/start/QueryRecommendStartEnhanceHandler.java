package com.sankuai.gaigc.arrange.common.core.bot.enhance.start;

import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.CompParamsUtil;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.BotEnhanceItem;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.FlowAccessConfig;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.QueryRecommendEnhance;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotEnhanceParallelResult;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotEnhanceResult;
import com.sankuai.gaigc.arrange.common.core.bot.enhance.BotStartEnhanceHandler;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotEnhanceRunMode;
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.FlowClient;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentParam;
import com.sankuai.gaigc.arrange.common.model.SSEMessage;
import com.sankuai.gaigc.arrange.common.util.ApplicationContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.util.*;

@Component
@Slf4j
public class QueryRecommendStartEnhanceHandler implements BotStartEnhanceHandler {
    private static final Integer CONTENT_SOURCE_FLOW_ACCESS = 3;

    @Override
    public void handle(BotEnhanceItem enhanceConfig, BotRunParam runParam, AIBot bot) {

    }

    @Override
    public String relateConfigClassName() {
        return QueryRecommendEnhance.class.getSimpleName();
    }

    @Override
    public BotEnhanceParallelResult parallelHandle(BotEnhanceItem enhanceConfig, BotRunParam runParam, AIBot bot) {
        BotEnhanceResult botEnhanceResult = new BotEnhanceResult();

        if (!(enhanceConfig instanceof QueryRecommendEnhance)) {
            return BotEnhanceParallelResult.builder().handler(this).result(botEnhanceResult).executeTimes(0l).build();
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        List<String> contents = Collections.emptyList();
        QueryRecommendEnhance recommendEnhance = (QueryRecommendEnhance) enhanceConfig;
        if (Objects.equals(CONTENT_SOURCE_FLOW_ACCESS, recommendEnhance.getContentSource()) && !recommendEnhance.getFlowAccessConfig().getEnableSerial()){
            contents = recommendOnFlowAccess(enhanceConfig,runParam);
        }
        botEnhanceResult.setQueryRecommend(contents);
        stopWatch.stop();
        return BotEnhanceParallelResult.builder().handler(this).result(botEnhanceResult).executeTimes(stopWatch.getTotalTimeSeconds()).build();
    }

    @Override
    public void afterParallelHandle(BotEnhanceParallelResult result, BotRunParam runParam,boolean isLastOne) {
        List<String> contents = result.getResult().getQueryRecommend();
        if (CollectionUtils.isEmpty(contents) || !StreamInfoHolder.availableStream(runParam.getStream())) {
            return;
        }
        // 发送流式消息
        SSEMessage sseMessage = SSEMessageUtils.generateQueryRecommendMessage(contents, result.getExecuteTimes(), SSEMessageUtils.generateSectionId());
        sseMessage.setIs_last_one(isLastOne);
        try {
            StreamInfoHolder.getEmitter().send(sseMessage);
        } catch (IOException e) {
            log.error("流式输出出错, botId:{}, e=", runParam.getBotId(), e);
        }
    }

    @Override
    public BotEnhanceRunMode mode() {
        return BotEnhanceRunMode.PARALLEL;
    }

    private List<String> recommendOnFlowAccess(BotEnhanceItem enhanceConfig, BotRunParam runParam) {
        FlowAccessConfig flowAccessConfig = ((QueryRecommendEnhance) enhanceConfig).getFlowAccessConfig();

        String flowId = flowAccessConfig.getFlowId();
        if (StringUtils.isBlank(flowId)) {
            log.warn("GuessYouWantEnhanceHandler#handle 未配置flowId");
            return Collections.emptyList();
        }

        try {
            ComponentInfo componentInfo = new ComponentInfo();
            Map<String, Object[]> requestParams = new HashMap<>();

            Map<String, Object> bizParams = runParam.getBizParams();

            Map<String, Object> flowInputMap = new HashMap<>();
            for (String key : bizParams.keySet()) {
                flowInputMap.put(key, bizParams.get(key));
            }
            requestParams.put("flowId", new Object[]{flowId, ParamTypeEnum.STRING});
            requestParams.put("inputs", new Object[]{flowInputMap, ParamTypeEnum.MAP});

            Map<String, ComponentParam> params = CompParamsUtil.buildInput(requestParams);
            componentInfo.setInputs(params);
            Context context = new Context();
            context.setEnvMap(new HashMap<>());
            Map<String, Object> executeResult = ApplicationContextUtil.getBean(FlowClient.class).execute(context, componentInfo);
            List<String> questionAndAnswer = (List<String>) executeResult.get("guessYouWant");
            if (CollectionUtils.isNotEmpty(questionAndAnswer) && StringUtils.isNotBlank(flowAccessConfig.getLimit())) {
                int limit = Math.min(questionAndAnswer.size(), NumberUtils.toInt(flowAccessConfig.getLimit(), questionAndAnswer.size()));
                questionAndAnswer = questionAndAnswer.subList(0, limit);
            }
            return questionAndAnswer;
        } catch (Exception e) {
            log.error("QueryRecommendEnhanceHandler#handle执行异常,enhanceConfig:{},runParam:{}", enhanceConfig, runParam, e);
            return Collections.emptyList();
        }
    }
}
