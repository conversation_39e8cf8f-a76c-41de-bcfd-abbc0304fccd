/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.meituan.nibhtp.os.htp.cms.access.common.search.Expression;
import com.meituan.nibhtp.os.htp.cms.access.common.search.Expressions;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.dto.datastream.DsKeywordQueryItemDto;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.dto.datastream.DsTextVectorQueryItemDto;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsKeywordQueryRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.req.datastream.DsTextVectorQueryRequest;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsKeywordQueryResponse;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.res.datastream.DsTextVectorQueryResponse;
import com.meituan.nibhtp.os.htp.cms.knowledge.build.client.service.datastream.IDataStreamQueryThriftService;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.constant.KnowledgeFieldConstants;
import com.sankuai.gaigc.arrange.common.core.bot.KnowledgeBase;
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotKnowledgeBaseService;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.BotLogCollect;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.CmsExpressionUtil;
import com.sankuai.gaigc.arrange.common.enums.RecallStrategyEnum;
import com.sankuai.gaigc.arrange.common.exception.RemoteServiceException;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.CmsStorageInfo;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseField;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeChunkContent;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeResultItem;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.RecallConfigInfo;
import com.sankuai.gaigc.arrange.dao.dal.enums.KnowledgeTypeEnum;
import com.sankuai.gaigc.arrange.dao.dal.enums.VecBaseEnum;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;

/**
 * 知识库检索
 *
 * <AUTHOR>
 * @created 2024/4/22
 */
@Slf4j
@PromptFlowComponent(name = "KnowledgeBaseRetrieve", desc = "知识库检索", type = ComponentTypeEnum.EMBEDDING_SEARCH, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "knowledgeBaseId", desc = "知识库ID", type = ParamTypeEnum.STRING, required = true, category = CategoryEnum.DEFAULT),
        @Param(name = "query", desc = "查询内容", type = ParamTypeEnum.STRING, required = true, category = CategoryEnum.DEFAULT),
        @Param(name = "topK", desc = "top数量", type = ParamTypeEnum.INT, required = false, category = CategoryEnum.DEFAULT),
        @Param(name = "expression", desc = "过滤表达式", type = ParamTypeEnum.STRING, required = false, category = CategoryEnum.DEFAULT),
        @Param(name = "similarityThreshold", desc = "最小匹配度（取值0到1）", type = ParamTypeEnum.DOUBLE, required = false, category = CategoryEnum.DEFAULT),
})

@OutputParamDefinition({
        @Param(name = "result", desc = "检索结果", type = ParamTypeEnum.JSON, required = true),
})
public class KnowledgeBaseRetrieve extends AbstractComponent {
    private static final String RESULT = "result";
    private static final String CMS_EXPRESSION = "expression";

    @Resource
    private AIBotKnowledgeBaseService knowledgeBaseService;
    @Resource(name = "dataStreamQueryThriftService")
    private IDataStreamQueryThriftService dataStreamQueryThriftService;
    @Resource
    private KnowledgeBaseScalarQuery knowledgeBaseScalarQuery;

    @BotLogCollect
    @Override
    public Map<String, Object> execute(Context context, ComponentInfo componentInfo) throws Exception {
        Map<String, Object> result = Maps.newHashMap();

        String knowledgeBaseId = (String) parseParam("knowledgeBaseId", componentInfo);
        String query = (String) parseParam("query", componentInfo);
        Integer topK = (Integer) Optional.ofNullable(parseParam("topK", componentInfo)).orElse(10);
        String expressionJson = (String) parseParam(CMS_EXPRESSION, componentInfo);
        Double similarityThreshold = (Double) Optional.ofNullable(parseParam("similarityThreshold", componentInfo)).orElse(0.0d);
        Expression expression = GsonUtil.fromJson(expressionJson, Expression.class);

        List<KnowledgeBase> knowledgeBases = knowledgeBaseService.queryByIds(Lists.newArrayList(Long.parseLong(knowledgeBaseId)));
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(knowledgeBases), "知识库[" + knowledgeBaseId + "]不存在");
        knowledgeBaseScalarQuery.checkAccessAuth(componentInfo.getAppId(), knowledgeBases);
        KnowledgeBase knowledgeBase = knowledgeBases.get(0);

        Transaction tran = Cat.newTransaction("KnowledgeBaseRetrieve", String.valueOf(knowledgeBase.getId()));
        try {
            // 表达式占位符、变量解析
            fillPhForExp(expression);

            KnowledgeTypeEnum knowledgeType = knowledgeBase.getKnowledgeType();
            if (KnowledgeTypeEnum.TEXT != knowledgeType) {
                throw new UnsupportedOperationException("暂不支持非文本类知识检索");
            }
            VecBaseEnum vecBase = knowledgeBase.getVecBase();
            if (VecBaseEnum.CMS != vecBase) {
                throw new UnsupportedOperationException("暂不支持非仓储存储的知识检索");
            }
            KnowledgeBaseRetrieveResult retrieveResult = dataStreamQuery(query, knowledgeBase, expression, topK);
            //匹配度过滤
            retrieveResult = filterBySimilarityThreshold(retrieveResult, similarityThreshold);

            result.put(RESULT, GsonUtil.toJson(retrieveResult));

            Cat.logEvent("KnowledgeRecallNotEmpty", String.valueOf(CollectionUtils.isNotEmpty(retrieveResult.getResult())));
            return result;
        } catch (Exception e) {
            log.error("知识库检索出错, knowledgeBaseId:{}, query:{}, topK:{}, e=", knowledgeBase, query, topK, e);
            tran.setStatus(e);
            throw e;
        } finally {
            tran.complete();
        }
    }

    private KnowledgeBaseRetrieveResult filterBySimilarityThreshold(KnowledgeBaseRetrieveResult retrieveResult, Double similarityThreshold) {
        if (Objects.isNull(retrieveResult) || CollectionUtils.isEmpty(retrieveResult.getResult()) || similarityThreshold <= 0.0d) {
            return retrieveResult;
        }
        List<KnowledgeResultItem> filterResult = retrieveResult.getResult().stream()
                .filter(knowledgeResultItem -> knowledgeResultItem.getMaxScore() >= similarityThreshold)
                .collect(Collectors.toList());
        log.info("KnowledgeBaseRetrieve similarity filter:before size {},after size() {}", retrieveResult.getResult().size(), filterResult.size());
        retrieveResult.setResult(filterResult);
        return retrieveResult;
    }

    private KnowledgeBaseRetrieveResult dataStreamQuery(String query, KnowledgeBase knowledgeBase, Expression expression, Integer topK) throws Exception {
        CmsStorageInfo cmsStorageInfo = knowledgeBase.getStorageInfo().getCmsStorageInfo();
        RecallConfigInfo recallConfig = knowledgeBase.getRecallConfig().getRecallConfig();
        RecallStrategyEnum recallStrategyEnum = RecallStrategyEnum.getEnumByValue(recallConfig.getRecallStrategy());
        Preconditions.checkArgument(Objects.nonNull(recallStrategyEnum), "召回策略不可为空");

        List<DsTextVectorQueryItemDto> queryResult;
        switch (recallStrategyEnum) {
            case SEMANTICS:
                queryResult = vectorQuery(cmsStorageInfo.getDataStreamId(), query, topK, expression, knowledgeBase);
                break;
            case FULL_TEXT:
                queryResult = fullTextSearchNormalize(cmsStorageInfo.getDataStreamId(), query, topK, expression, knowledgeBase, Collections.emptyList());
                break;
            case BLEND:
                Double recallRatio = recallConfig.getRecallRatio();
                Integer vecTopK = (int) Math.ceil(topK * recallRatio);
                Integer fullTextTopK = (int) Math.ceil(topK * (1 - recallRatio));
                queryResult = vectorQuery(cmsStorageInfo.getDataStreamId(), query, vecTopK, expression, knowledgeBase);
                List<Long> vectorDocIds = queryResult.stream().map(DsTextVectorQueryItemDto::getDocId).collect(Collectors.toList());
                List<DsTextVectorQueryItemDto> fullTextSearchResult = fullTextSearchNormalize(cmsStorageInfo.getDataStreamId(), query, fullTextTopK, expression, knowledgeBase, vectorDocIds);
                if (CollectionUtils.isNotEmpty(fullTextSearchResult)) {
                    queryResult.addAll(fullTextSearchResult);
                    queryResult = queryResult.stream().sorted(Comparator.comparing(DsTextVectorQueryItemDto::getMaxScore).reversed()).collect(Collectors.toList());
                }
                break;
            default:
                throw new IllegalArgumentException("不支持的召回策略");
        }

        // 组装查询结果
        KnowledgeBaseRetrieveResult retrieveResult = new KnowledgeBaseRetrieveResult();
        retrieveResult.setKnowledgeBaseId(knowledgeBase.getId());
        retrieveResult.setKnowledgeBaseName(knowledgeBase.getName());
        retrieveResult.setResult(queryResult.stream().map(this::convert).collect(Collectors.toList()));
        return retrieveResult;
    }

    private List<DsTextVectorQueryItemDto> fullTextSearchNormalize(Long dataStreamId, String query, Integer topK, Expression expression, KnowledgeBase knowledgeBase, List<Long> vectorDocIds) {
        if (topK <= 0) {
            return Collections.emptyList();
        }

        List<KnowledgeBaseField> fullTextIndexFields = knowledgeBase.getFields().stream().filter(KnowledgeBaseField::isAsFullTextIndex).collect(Collectors.toList());
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(fullTextIndexFields), "full text index field cannot be empty for full text search.");
        List<Expression> fullTextSearchOrExps = Lists.newArrayList();
        for (KnowledgeBaseField fullTextIndexField : fullTextIndexFields) {
            fullTextSearchOrExps.add(Expressions.directMatch(fullTextIndexField.getFieldName(), query));
        }
        Expression textSearchExp = fullTextSearchOrExps.size() == 1 ? fullTextSearchOrExps.get(0) : Expressions.or(fullTextSearchOrExps);
        // 全文检索表达式
        Expression fullTextExp = CmsExpressionUtil.andExpComposition(expression, textSearchExp);
        List<DsKeywordQueryItemDto> keywordQueryItemDtos = keywordQuery(dataStreamId, topK, fullTextExp);
        if (CollectionUtils.isEmpty(keywordQueryItemDtos)) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(knowledgeBase.getEbdModel())) {
            // 没有向量数据，直接将全文检索结果转化输出
            return keywordQueryItemDtos.stream().map(this::convertToDsTextVectorQueryItemDto).collect(Collectors.toList());
        }
        List<String> docIds = keywordQueryItemDtos.stream().map(DsKeywordQueryItemDto::getDocId).distinct().map(String::valueOf).collect(Collectors.toList());
        docIds.removeAll(vectorDocIds.stream().map(String::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(docIds)) {
            return Collections.emptyList();
        }
        // 向量查询表达式，二次查询做归一化
        Expression vectorExp = Expressions.terms(KnowledgeFieldConstants.DOC_ID, docIds);
        return vectorQuery(dataStreamId, query, topK * 3, vectorExp, knowledgeBase);
    }

    private List<DsTextVectorQueryItemDto> vectorQuery(Long dataStreamId, String query, Integer topK, Expression expression, KnowledgeBase knowledgeBase) {
        if (topK <= 0) {
            return Collections.emptyList();
        }

        DsTextVectorQueryRequest request = new DsTextVectorQueryRequest();
        request.setDataStreamId(dataStreamId);
        request.setQuery(query);
        request.setTopK(topK);
        request.setScalarSearchParam(CmsExpressionUtil.buildQueryCondition(expression));
        request.setEbdModel(knowledgeBase.getEbdModel());

        try {
            DsTextVectorQueryResponse response = dataStreamQueryThriftService.textVectorQuery(request);
            if (!response.isSuccess()) {
                log.error("数据流向量查询失败, request:{}, response:{}", request, response);
                throw new RemoteServiceException("数据流向量查询失败");
            }
            return response.getData();
        } catch (TException e) {
            log.error("数据流向量查询出错, request:{}, e=", request, e);
            throw new RemoteServiceException("数据流向量查询出错", e);
        }
    }

    private List<DsKeywordQueryItemDto> keywordQuery(Long dataStreamId, Integer topK, Expression expression) {
        DsKeywordQueryRequest request = new DsKeywordQueryRequest();
        request.setDataStreamId(dataStreamId);
        request.setTopK(topK);
        request.setScalarSearchParam(CmsExpressionUtil.buildQueryCondition(expression));

        try {
            DsKeywordQueryResponse response = dataStreamQueryThriftService.keywordQuery(request);
            if (!response.isSuccess()) {
                log.error("数据流关键词查询失败, request:{}, response:{}", request, response);
                throw new RemoteServiceException("数据流关键词查询失败");
            }
            return response.getData();
        } catch (TException e) {
            log.error("数据流关键词查询出错, request:{}, e=", request, e);
            throw new RemoteServiceException("数据流关键词查询出错", e);
        }
    }

    private KnowledgeResultItem convert(DsTextVectorQueryItemDto queryItemDto) {
        KnowledgeResultItem resultItem = new KnowledgeResultItem();
        resultItem.setDocId(queryItemDto.getDocId());
        resultItem.setMaxScore(queryItemDto.getMaxScore());
        resultItem.setMinScore(queryItemDto.getMinScore());
        Map<String, Object> fieldData = GsonUtil.fromJson(queryItemDto.getJsonData(), new TypeToken<Map<String, Object>>() {
        }.getType());
        resultItem.setFieldData(Optional.ofNullable(fieldData).orElse(Collections.emptyMap()));

        List<KnowledgeChunkContent> chunkContents = Optional.ofNullable(queryItemDto.getChunkContents()).orElse(Collections.emptyList())
                .stream().map(item -> {
                    KnowledgeChunkContent chunkContent = new KnowledgeChunkContent();
                    chunkContent.setJsonData(item.getJsonData());
                    chunkContent.setChunk(item.getChunk());
                    chunkContent.setEbdFieldName(item.getEbdFieldName());
                    chunkContent.setScore(item.getScore());
                    chunkContent.setSeq(item.getSeq());
                    return chunkContent;
                }).collect(Collectors.toList());
        resultItem.setChunkContents(chunkContents);

        if (MapUtils.isEmpty(resultItem.getFieldData()) && chunkContents.size() == 1) {
            // 兼容只有单个向量索引的使用场景
            KnowledgeChunkContent chunkContent = chunkContents.get(0);
            fieldData = GsonUtil.fromJson(chunkContent.getJsonData(), new TypeToken<Map<String, Object>>() {
            }.getType());
            resultItem.setFieldData(Optional.ofNullable(fieldData).orElse(Collections.emptyMap()));
        }

        return resultItem;
    }

    private DsTextVectorQueryItemDto convertToDsTextVectorQueryItemDto(DsKeywordQueryItemDto queryItemDto) {
        DsTextVectorQueryItemDto vectorQueryItemDto = new DsTextVectorQueryItemDto();
        vectorQueryItemDto.setDocId(queryItemDto.getDocId());
        vectorQueryItemDto.setJsonData(queryItemDto.getJsonData());
        vectorQueryItemDto.setMaxScore(queryItemDto.getSimScore());
        vectorQueryItemDto.setMinScore(queryItemDto.getSimScore());
        vectorQueryItemDto.setChunkContents(Collections.emptyList());

        return vectorQueryItemDto;
    }

}
