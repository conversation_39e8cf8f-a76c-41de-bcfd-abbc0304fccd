package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mvp.facade.thrift.SearchRequest;
import com.meituan.mvp.facade.thrift.SearchResponse;
import com.meituan.mvp.facade.thrift.TFacadeService;
import com.meituan.service.inf.kms.client.Kms;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.constant.ServiceConstant;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.remoteproxy.IEagleElasticSearchService;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.Expression2DslUtil;
import com.sankuai.gaigc.arrange.common.core.promptflow.util.StringUtil;
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;


import static com.sankuai.gaigc.arrange.common.constant.ServiceConstant.APP_KEY;


/**
 * @Author: wenhao10
 * @Description:
 * @Date: 2023-08-15 16:41
 */
@PromptFlowComponent(name = "GeneralEmbeddingSearch", desc = "通用向量化搜索组件", type = ComponentTypeEnum.EMBEDDING_SEARCH, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "cluster", desc = "集群名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "accessKeyName", desc = "access信息-KMS秘钥名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "accessAppKey", desc = "access信息-KMS秘钥关联appKey", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "scoreFilterThreshold", desc = "相关性过滤阈值", type = ParamTypeEnum.DOUBLE, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "collection", desc = "索引名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "topK", desc = "返回topK条数据", type = ParamTypeEnum.INT, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "vectors", desc = "待计算相似度的向量", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "expr", desc = "标量过滤条件", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "outFields", desc = "期望召回的索引字段", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "tenant", desc = "租户名称", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "openEsDegrade", desc = "是否开启ES降级", type = ParamTypeEnum.BOOLEAN, required = false, category = CategoryEnum.ADVANCED),
        @Param(name = "indexName", desc = "ES索引名称", type = ParamTypeEnum.STRING, category = CategoryEnum.ADVANCED, required = false),
        @Param(name = "nestFieldName", desc = "ES嵌套字段名称", type = ParamTypeEnum.STRING, category = CategoryEnum.ADVANCED, required = false),
        @Param(name = "query", desc = "查询文本", type = ParamTypeEnum.STRING, category = CategoryEnum.ADVANCED, required = false),
        @Param(name = "embeddingFields", desc = "ES中用于向量化的字段", type = ParamTypeEnum.STRING, category = CategoryEnum.ADVANCED, required = false),
        @Param(name = "degradeExcludeFields", desc = "降级排除字段", type = ParamTypeEnum.STRING, category = CategoryEnum.ADVANCED, required = false),
        @Param(name = "ef", desc = "搜索范围", type = ParamTypeEnum.INT, required = false, category = CategoryEnum.ADVANCED),
})
@OutputParamDefinition({
        @Param(name = "text", desc = "实体的文本", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "score", desc = "距离或相似性的值", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "data", desc = "查询结果的属性列表", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = true)
})
@Slf4j
public class GeneralEmbeddingSearch extends AbstractComponent {
    @Autowired
    TFacadeService.Iface dialogServiceClient;
    @Resource
    private IEagleElasticSearchService aigcDefaultEagleElasticSearchService;
    private static final String VECTOR_DATA_BASE_ACCESS_KEY = Lion.getString(APP_KEY, "vector.access.key");
    public static final String VECTOR_DATA_BASE_TENANT = Lion.getString(APP_KEY, "vector.base.tenant");

    private static final String EXPR_KEY = "expr";


    @Override
    public Map<String, Object> execute(Context context, ComponentInfo componentInfo) throws Exception {
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.setCluster((String) parseParam("cluster", componentInfo));
        searchRequest.setCollection((String) parseParam("collection", componentInfo));
        searchRequest.setTopK((Integer) parseParam("topK", componentInfo));
        searchRequest.setVectors((List<Double>) parseParam("vectors", componentInfo));
        searchRequest.setExpr(parseSubJsonPathParam("expr", componentInfo));
        searchRequest.setVectorFieldName("embedding");

        searchRequest.setCluster((String) parseParam("cluster", componentInfo));
        String tenant = (String) parseParam("tenant", componentInfo);
        searchRequest.setTenant(StringUtils.isNotEmpty(tenant) ? tenant : GeneralEmbeddingSearch.VECTOR_DATA_BASE_TENANT);

        String outFields = (String) parseParam("outFields", componentInfo);
        List<String> outFieldsList = Arrays.asList(outFields.split(","));
        searchRequest.setOutFields(outFieldsList);
        //获取秘钥
        String accessKeyName = (String) parseParam("accessKeyName", componentInfo);
        String accessAppKey = (String) parseParam("accessAppKey", componentInfo);
        Double scoreFilterThreshold = (Double) parseParam("scoreFilterThreshold", componentInfo);
        accessAppKey = StringUtils.isNotEmpty(accessAppKey) ? accessAppKey : ServiceConstant.APP_KEY;
        searchRequest.setAccessKey(StringUtils.isNotEmpty(accessKeyName) ? Kms.getByName(accessAppKey, accessKeyName) : VECTOR_DATA_BASE_ACCESS_KEY);
        // Milvus扩展参数
        Map<String, String> ext = Maps.newHashMap();
        Integer ef = (Integer) parseParam("ef", componentInfo);
        Optional.ofNullable(ef).ifPresent(efContent -> {
            Map<String, Object> params = Maps.newHashMap();
            params.put("ef", efContent);
            ext.put("params", GsonUtil.toJson(params));
        });
        searchRequest.setExt(ext);

        SearchResponse search = null;
        long startTime = System.currentTimeMillis();
        Transaction tran = Cat.newTransaction("MilvusVectorQuery", searchRequest.getCollection());
        try {
            search = dialogServiceClient.search(searchRequest);
            //rpcSuccessLog("dialogServiceClient", componentInfo, genRpcParam(filterEmbedding(searchRequest)), genRpcParam(search), startTime);
        } catch (Exception e) {
            tran.setStatus(e);
            //rpcErrorLog("dialogServiceClient", componentInfo, genRpcParam(filterEmbedding(searchRequest)), null, startTime, e);
            Boolean openEsDegrade = Optional.ofNullable((Boolean) parseParam("openEsDegrade", componentInfo)).orElse(Boolean.FALSE);
            if (BooleanUtils.isTrue(openEsDegrade)) {
                Map<String, Object> objectMap = degradeEsQueryCatchException(searchRequest, scoreFilterThreshold, componentInfo);
                if (Objects.nonNull(objectMap)) {
                    return objectMap;
                }
            }
            throw e;
        } finally {
            tran.complete();
        }
        Map<String, Object> result = parseSearchResult(search, outFieldsList, scoreFilterThreshold, searchRequest, componentInfo);
        return result;
    }

    private SearchRequest filterEmbedding(SearchRequest searchRequest) {
        if (Objects.nonNull(searchRequest)){
            searchRequest.setVectors(Lists.newArrayList());
        }
        return searchRequest;
    }

    private Map<String, Object> degradeEsQueryCatchException(SearchRequest searchRequest, Double minScore, ComponentInfo componentInfo) throws Exception {
        long startTime = System.currentTimeMillis();
        Transaction tran = Cat.newTransaction("MilvusDegradeEsQuery", searchRequest.getCollection());
        try {
            Map<String, Object> objectMap = degradeEsQuery(searchRequest, minScore, componentInfo);
            //rpcSuccessLog("eagleRestClient", componentInfo, genRpcParam(filterEmbedding(searchRequest)), genRpcParam(objectMap), startTime);
            return objectMap;
        } catch (Exception e) {
            tran.setStatus(e);
            log.error("milvus degrade es query error, searchRequest:{}, minScore:{}, e=", filterEmbedding(searchRequest), minScore, e);
            //rpcErrorLog("eagleRestClient", componentInfo, genRpcParam(filterEmbedding(searchRequest)), null, startTime, e);
            throw e;
        } finally {
            tran.complete();
        }
    }

    private Map<String, Object> degradeEsQuery(SearchRequest searchRequest, Double minScore, ComponentInfo componentInfo) throws Exception {
        String indexName = (String) parseParam("indexName", componentInfo);
        String nestFieldName = (String) parseParam("nestFieldName", componentInfo);
        String query = (String) parseParam("query", componentInfo);
        String embeddingFieldStr = (String) Optional.ofNullable(parseParam("embeddingFields", componentInfo)).orElse("");
        String degradeExcludeFieldStr = (String) Optional.ofNullable(parseParam("degradeExcludeFields", componentInfo)).orElse("");
        if (StringUtils.isBlank(indexName)) {
            log.info("degrade es query failure, missing downgrade description information.");
            return null;
        }
        List<String> embeddingFields = Arrays.stream(embeddingFieldStr.split(",")).collect(Collectors.toList());
        Set<String> degradeExcludeFields = Arrays.stream(degradeExcludeFieldStr.split(",")).collect(Collectors.toSet());
        List<String> outFields = searchRequest.getOutFields();

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(generalEsQuery(query, searchRequest.getExpr(), nestFieldName, embeddingFields, degradeExcludeFields));
        sourceBuilder.size(searchRequest.getTopK());
        org.elasticsearch.action.search.SearchRequest request = new org.elasticsearch.action.search.SearchRequest();
        request.indices(indexName)
                .searchType(SearchType.QUERY_THEN_FETCH)
                .source(sourceBuilder);
        org.elasticsearch.action.search.SearchResponse response = aigcDefaultEagleElasticSearchService.search(request, RequestOptions.DEFAULT);
        SearchHits hits = response.getHits();
        if (Objects.isNull(hits)) {
            return Collections.emptyMap();
        }

        Map<String, Object> result = Maps.newHashMap();
        Map<Map<String, Object>, Double> docsAndScore = Maps.newHashMap();
        for (SearchHit hit : hits) {
            Map<String, Object> docs = hit.getSourceAsMap();
            if (MapUtils.isEmpty(docs)) {
                continue;
            }
            Map<String, Object> rowContent = getDocs(docs, nestFieldName);
            Double score = getScoreForDoc(rowContent, query, embeddingFields);
            if (StringUtils.isNotBlank(query) && Objects.nonNull(minScore) && score < minScore) {
                continue;
            }
            docsAndScore.put(docs, score);
        }
        Comparator<Map.Entry<Map<String, Object>, Double>> compareBySort = Map.Entry.comparingByValue();
        List<Map.Entry<Map<String, Object>, Double>> entryList = docsAndScore.entrySet().stream().sorted(compareBySort.reversed()).collect(Collectors.toList());

        List<Map<String, String>> dataList = Lists.newArrayList();
        List<String> docList = Lists.newArrayList();
        List<String> scoreList = Lists.newArrayList();

        for (Map.Entry<Map<String, Object>, Double> mapEntry : entryList) {
            Map<String, Object> docs = mapEntry.getKey();
            Double score = mapEntry.getValue();

            Map<String, Object> rowContent = getDocs(docs, nestFieldName);
            scoreList.add(String.valueOf(score));
            Object doc = docs.get("doc");
            docList.add(Objects.isNull(doc) ? null : doc.toString());
            dataList.add(outFields.stream().collect(HashMap::new, (map, f) -> {
                Object value = rowContent.get(f);
                map.put(f, Objects.nonNull(value) ? String.valueOf(value) : null);
            }, HashMap::putAll));
        }

        result.put("text", docList);
        result.put("score", scoreList);
        result.put("data", dataList);
        return result;
    }

    private QueryBuilder generalEsQuery(String query, String exp, String nestFieldName, List<String> embeddingFields, Set<String> degradeExcludeFields) {
        BoolQueryBuilder queryBuilder = StringUtils.isBlank(exp) ? new BoolQueryBuilder() : (BoolQueryBuilder) Expression2DslUtil.convertDsl(exp, nestFieldName, degradeExcludeFields);
        if (StringUtils.isNotBlank(query) && CollectionUtils.isNotEmpty(embeddingFields)) {
            BoolQueryBuilder queryMatch = new BoolQueryBuilder();
            for (String embeddingField : embeddingFields) {
                String field = StringUtils.isBlank(nestFieldName) ? embeddingField : nestFieldName + "." + embeddingField;
                queryMatch.should(QueryBuilders.matchQuery(field, query));
            }
            queryBuilder.must(queryMatch);
        }

        if (StringUtils.isNotBlank(nestFieldName)) {
            BoolQueryBuilder result = new BoolQueryBuilder();
            result.must(QueryBuilders.nestedQuery(nestFieldName, queryBuilder, ScoreMode.Max));
            return result;
        }
        return queryBuilder;
    }

    private Map<String, Object> getDocs(Map<String, Object> docs, String nestFieldName) {
        if (StringUtils.isBlank(nestFieldName)) {
            return docs;
        }
        if (docs.containsKey(nestFieldName)) {
            Map<String, Object> result = Maps.newHashMap();
            for (Map.Entry<String, Object> entry : docs.entrySet()) {
                String fieldName = entry.getKey();
                if (StringUtils.equals(fieldName, nestFieldName)) {
                    continue;
                }
                result.put(fieldName, entry.getValue());
            }
            result.putAll((Map<String, Object>) docs.get(nestFieldName));
            return result;
        }
        throw new IllegalArgumentException("nest field " + nestFieldName + " not found");
    }

    private Double getScoreForDoc(Map<String, Object> rowContent, String query, List<String> embeddingFields) {
        if (StringUtils.isBlank(query) || CollectionUtils.isEmpty(embeddingFields)) {
            return 0D;
        }

        Double max = Double.MIN_VALUE;
        for (String embeddingField : embeddingFields) {
            if (!rowContent.containsKey(embeddingField)) {
                continue;
            }
            max = Math.max(max, StringUtil.cosineSimilarity(query, rowContent.get(embeddingField).toString()));
        }
        if (max.compareTo(Double.MIN_VALUE) == 0) {
            return 0D;
        }
        return max;
    }

    private Map<String, Object> parseSearchResult(SearchResponse search, List<String> outFields, Double minScore, SearchRequest searchRequest, ComponentInfo componentInfo) throws Exception {
        Map<String, Object> result = Maps.newHashMap();
        if (search.getCode() == 200) {
            List<Map<String, String>> data = search.getData();
            data = filterByScore(data, minScore);
            List<String> docList = data.stream().map(d -> d.get("doc")).collect(Collectors.toList());
            result.put("text", docList);
            List<String> scoreList = data.stream().map(d -> d.get("score")).collect(Collectors.toList());
            result.put("score", scoreList);
            List<Map<String, String>> dataList = data.stream().map(item -> {
                Map<String, String> map = new HashMap<>();
                outFields.forEach(field -> map.put(field, item.get(field)));
                return map;
            }).collect(Collectors.toList());
            result.put("data", dataList);
        } else {
            Boolean openEsDegrade = Optional.ofNullable((Boolean) parseParam("openEsDegrade", componentInfo)).orElse(Boolean.FALSE);
            if (BooleanUtils.isTrue(openEsDegrade)) {
                Map<String, Object> objectMap = degradeEsQueryCatchException(searchRequest, minScore, componentInfo);
                if (Objects.nonNull(objectMap)) {
                    return objectMap;
                }
            }
            log.error("query search code != 200 result = {}", search);
            result.put("text", new ArrayList<String>());
            result.put("score", new ArrayList<String>());
            result.put("data", new ArrayList<HashMap<String, String>>());
        }
        return result;
    }

    private List<Map<String, String>> filterByScore(List<Map<String, String>> originData, Double minScore) {
        List<Map<String, String>> result = Lists.newArrayList();

        if (minScore != null && minScore > 0.0) {
            for (Map<String, String> singleData : originData) {
                String score = singleData.get("score");
                if (StringUtils.isNotEmpty(score) && NumberUtils.isNumber(score)) {
                    double scoreDouble = NumberUtils.toDouble(score);
                    if (scoreDouble >= minScore) {
                        result.add(singleData);
                    }
                }
            }
            return result;
        } else {
            return originData;
        }
    }


}
