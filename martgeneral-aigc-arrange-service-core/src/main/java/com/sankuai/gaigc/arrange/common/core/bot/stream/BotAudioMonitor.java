package com.sankuai.gaigc.arrange.common.core.bot.stream;

import com.sankuai.ai.speech.platform.sdk.tts.response.StreamResponse;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * <AUTHOR>
 * @date 2025/2/20 13:25
 * @desc
 */
public class BotAudioMonitor {
    private static ConcurrentHashMap <AudioWrapperSseEmitter, LinkedBlockingQueue<StreamResponse>> monitorMap = new ConcurrentHashMap<>();

    static {
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true){
                    for (Map.Entry<AudioWrapperSseEmitter, LinkedBlockingQueue<StreamResponse>> entry : monitorMap.entrySet()) {
                        AudioWrapperSseEmitter emitter = entry.getKey();
                        LinkedBlockingQueue<StreamResponse> queue = entry.getValue();
                        StreamResponse streamResponse = queue.peek();
                        if (streamResponse != null) {
                            byte[] audioData = streamResponse.getAudioData();
                            try {
                                emitter.send(audioData);
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }
                }
            }
        }).start();
    }

    public static void submit(AudioWrapperSseEmitter emitter, LinkedBlockingQueue<StreamResponse> queue){
        monitorMap.put(emitter, queue);
    }

    public static void remove(AudioWrapperSseEmitter emitter){
        monitorMap.remove(emitter);

        emitter.latch.countDown();
    }



}
