package com.sankuai.gaigc.arrange.common.core.stream;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import com.meituan.ai.friday.sdk.api.completion.chat.ChatCompletionRequest;
import com.meituan.mdp.langmodel.api.Constants;
import com.meituan.mdp.langmodel.api.function.MdpChatFunction;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.MdpChatCompletionRequest;
import com.meituan.mdp.langmodel.api.message.Message;
import com.meituan.mdp.langmodel.api.model.StreamResponseHandler;
import com.meituan.mdp.langmodel.api.model.chat.StreamChatLanguageModel;
import com.meituan.mdp.langmodel.component.function.MdpFunctionRegistry;
import com.meituan.mdp.langmodel.component.model.chat.stream.FridayStreamChatLanguageModelRequester;
import com.meituan.mdp.langmodel.component.properties.model.friday.FridayModelProperties;
import com.meituan.mdp.langmodel.component.utils.*;
import com.meituan.mtrace.Tracer;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FridayStreamChatLanguageModelWithFunc implements StreamChatLanguageModel {
    private static final Logger log = LoggerFactory.getLogger(FridayStreamChatLanguageModelWithFunc.class);
    private final String fridayAppId;
    private final FridayStreamChatLanguageModelRequester requester;
    List<String> stop = null;
    Map<String, Integer> logitBias = null;
    private FridayModelProperties properties = ModelHelper.defaultChatFridayModelProperties();

    public FridayStreamChatLanguageModelWithFunc(String fridayAppId, boolean fridayDebug) {
        this.fridayAppId = fridayAppId;
        this.properties.setDebug(fridayDebug);
        this.requester = new FridayStreamChatLanguageModelRequester();
    }

    public FridayStreamChatLanguageModelWithFunc(String fridayAppId, FridayStreamChatLanguageModelRequester requester, FridayModelProperties properties) {
        this.fridayAppId = fridayAppId;
        this.requester = requester;
        this.properties = properties;
    }

    public AssistantMessage sendMessages(List<Message> msgList, StreamResponseHandler<AssistantMessage> handler) {
        MdpChatCompletionRequest request = this.generateRequest(msgList);
        if (this.isDebugEnabled()) {
            log.info("FridayChatLanguageModel#sendMessages, msgList={}, request={}", SerializeUtils.toJsonStr(msgList), SerializeUtils.toJsonStr(request));
        }

        return this.httpPost(request, handler);
    }


    public AssistantMessage sendMessagesUseCustomFunc(List<Message> msgList, List<?> chatFunctionList, StreamResponseHandler<AssistantMessage> handler) {
        MdpChatCompletionRequest request = ModelHelper.generateRequest(msgList, this.properties, chatFunctionList);
        request.setStream(true);
        return this.httpPost(request, handler);
    }

    public AssistantMessage sendMessagesUseFuncNames(List<Message> msgList, List<String> funcNameList, StreamResponseHandler<AssistantMessage> handler) {
        if (CollectionUtils.isEmpty(funcNameList)) {
            throw new RuntimeException("Function list is empty");
        } else {
            MdpChatCompletionRequest request = this.generateFunctionRequest(msgList, MdpFunctionRegistry.getFunctions(funcNameList));
            if (this.isDebugEnabled()) {
                log.info("FridayChatLanguageModel#sendMessagesUseFuncNames, msgList={}, funcNameList={}, request={}", new Object[]{SerializeUtils.toJsonStr(msgList), SerializeUtils.toJsonStr(funcNameList), SerializeUtils.toJsonStr(request)});
            }

            return this.httpPost(request, handler);
        }
    }

    private AssistantMessage httpPost(MdpChatCompletionRequest request, StreamResponseHandler<AssistantMessage> handler) {
        try {
            log.info("FridayChatLanguageModel#httpPost, request={}", SerializeUtils.toJsonStr(request));

            return FridayHttpUtils.doChatStreamingPost(this.fridayAppId, SerializeUtils.toJsonStr(request), handler);
        } catch (Exception var4) {
            throw new RuntimeException(var4);
        }
    }

    private boolean isDebugEnabled() {
        return false;
    }

    private MdpChatCompletionRequest generateRequest(List<Message> msgList) {
        if (CollectionUtils.isEmpty(msgList)) {
            throw new IllegalArgumentException("param msgList can not empty");
        } else {
            ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder().model(this.properties.getModel()).messages(ModelHelper.toFridayChatMessages(msgList)).user(Tracer.id()).temperature(this.properties.getTemperature()).topP(this.properties.getTopP()).n(Constants.N).stream(Constants.IS_STREAM).stop(this.stop).maxTokens(this.properties.getMaxTokens()).presencePenalty(this.properties.getPresencePenalty()).frequencyPenalty(this.properties.getFrequencyPenalty()).logitBias(this.logitBias).build();
            return BeanCopyUtils.to(chatCompletionRequest);
        }
    }

    private MdpChatCompletionRequest generateFunctionRequest(List<Message> msgList, List<MdpChatFunction> chatFunctionList) {
        if (CollectionUtils.isEmpty(chatFunctionList)) {
            throw new IllegalArgumentException("chatFunctionList can not empty");
        } else {
            MdpChatCompletionRequest chatCompletionRequest = this.generateRequest(msgList);
            chatCompletionRequest.setFunctions(chatFunctionList);
            if (this.isDebugEnabled()) {
                log.info("FridayChatLanguageModel#generateChatCompletionRequest, msgList={}, chatFunctionList={}, chatCompletionRequest={}", new Object[]{SerializeUtils.toJsonStr(msgList), SerializeUtils.toJsonStr(chatFunctionList), SerializeUtils.toJsonStr(chatCompletionRequest)});
            }

            return chatCompletionRequest;
        }
    }

    public FridayModelProperties getProperties() {
        return this.properties;
    }

    public static FridayStreamChatLanguageModelBuilder builder() {
        return new FridayStreamChatLanguageModelBuilder();
    }

    public static class FridayStreamChatLanguageModelBuilder {
        private String fridayAppId;
        private FridayStreamChatLanguageModelRequester requester;
        private FridayModelProperties properties;

        FridayStreamChatLanguageModelBuilder() {
        }

        public FridayStreamChatLanguageModelBuilder fridayAppId(final String fridayAppId) {
            this.fridayAppId = fridayAppId;
            return this;
        }

        public FridayStreamChatLanguageModelBuilder requester(final FridayStreamChatLanguageModelRequester requester) {
            this.requester = requester;
            return this;
        }

        public FridayStreamChatLanguageModelBuilder properties(final FridayModelProperties properties) {
            this.properties = properties;
            return this;
        }

        public FridayStreamChatLanguageModelWithFunc build() {
            return new FridayStreamChatLanguageModelWithFunc(this.fridayAppId, this.requester, this.properties);
        }

        public String toString() {
            return "FridayStreamChatLanguageModel.FridayStreamChatLanguageModelBuilder(fridayAppId=" + this.fridayAppId + ", requester=" + this.requester + ", properties=" + this.properties + ")";
        }
    }
}
