package com.sankuai.gaigc.arrange.common.core.promptflow.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * IHotelAssistant.question 入参model特供枚举
 * 张俊杰 2025年01月13日17:48:36
 */
@AllArgsConstructor
@Getter
public enum HotelAssistantQuestionModelEnum {

    INTENT("intent", "意图识别"),
    CHAT("chat", "聊天"),
    RELAX("relax", "度假助手");

    private final String code;
    private final String desc;

}
