/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.bot.service.impl;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.apolloinfra.crm.saleskits.api.dto.bot.CrmAiKnowledgeDTO;
import com.sankuai.apolloinfra.crm.saleskits.api.dto.bot.KnowledgeAuthDTO;
import com.sankuai.apolloinfra.crm.saleskits.api.request.bot.KnowledgeAuthRequest;
import com.sankuai.apolloinfra.crm.saleskits.api.response.bot.KnowledgeAuthResponse;
import com.sankuai.apolloinfra.crm.saleskits.api.service.bot.TAIBotKnowledgeAuthService;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.BotKnowledgeAuthConfig;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.CrmKnowledgeAuthConfig;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.UserModel;
import com.sankuai.gaigc.arrange.common.core.bot.service.BotKnowledgeAuthService;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult;
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeResultItem;
import com.sankuai.gaigc.arrange.dao.dal.enums.KnowledgeBaseFieldTypeEnum;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * CrmSaleToolsAuthServiceImpl
 *
 * <AUTHOR>
 * @created 2024/6/2
 */
@Slf4j
@Service
public class CrmSaleToolsAuthServiceImpl implements BotKnowledgeAuthService {
    private static final Integer HOTEL_TENANT_ID = 2;
    @MdpThriftClient(remoteAppKey = "com.sankuai.apolloinfra.crm.saleskits", timeout = 3000)
    private TAIBotKnowledgeAuthService tAiBotKnowledgeAuthService;

    @Override
    public Map<String, KnowledgeBaseRetrieveResult> authData(Map<String, KnowledgeBaseRetrieveResult> retrieveResult, UserModel user, BotKnowledgeAuthConfig authConfig) {
        Preconditions.checkArgument(Objects.nonNull(authConfig.getCrmAuthConfig()), "CRM鉴权配置不可为空");
        CrmKnowledgeAuthConfig crmAuthConfig = authConfig.getCrmAuthConfig();

        List<CrmAiKnowledgeDTO> dataList = retrieveResult.values().stream().flatMap(subRetrieveResult -> subRetrieveResult.getResult().stream())
                .map(knowledgeResultItem -> {
                    Map<String, Object> fieldData = knowledgeResultItem.getFieldData();

                    CrmAiKnowledgeDTO knowledgeDTO = new CrmAiKnowledgeDTO();
                    knowledgeDTO.setTenantId(HOTEL_TENANT_ID);
                    Long id = KnowledgeBaseFieldTypeEnum.LONG.getLongFieldValue(fieldData, crmAuthConfig.getIdFieldName());
                    String channel = KnowledgeBaseFieldTypeEnum.STRING.getStringFieldValue(fieldData, crmAuthConfig.getChannelFieldName());
                    if (Objects.isNull(id) || Objects.isNull(channel)) {
                        return null;
                    }
                    knowledgeDTO.setId(id);
                    knowledgeDTO.setChannel(channel);
                    return knowledgeDTO;
                }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, Boolean> authResult = auth(dataList, user.getUserId().intValue());

        Map<String, KnowledgeBaseRetrieveResult> authResultMap = Maps.newHashMap();
        for (Map.Entry<String, KnowledgeBaseRetrieveResult> resultEntry : retrieveResult.entrySet()) {
            String ph = resultEntry.getKey();
            KnowledgeBaseRetrieveResult subRetrieveResult = resultEntry.getValue();

            List<KnowledgeResultItem> authPassItems = subRetrieveResult.getResult().stream().filter(knowledgeResultItem -> {
                Object id = knowledgeResultItem.getFieldData().get(crmAuthConfig.getIdFieldName());
                if (Objects.isNull(id) || !StringUtils.isNumeric(id.toString())) {
                    return false;
                }
                return BooleanUtils.isTrue(authResult.get(Long.parseLong(id.toString())));
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(authPassItems)) {
                continue;
            }
            KnowledgeBaseRetrieveResult authPassResult = new KnowledgeBaseRetrieveResult();
            BeanUtils.copyProperties(subRetrieveResult, authPassResult);
            authPassResult.setResult(authPassItems);
            authResultMap.put(ph, authPassResult);
        }
        return authResultMap;
    }

    private Map<Long, Boolean> auth(List<CrmAiKnowledgeDTO> dataList, Integer userId) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Maps.newHashMap();
        }
        KnowledgeAuthRequest request = new KnowledgeAuthRequest();
        request.setKnowledgeDTOS(dataList);
        request.setUserId(userId);
        KnowledgeAuthResponse response = tAiBotKnowledgeAuthService.authKnowledges(request);
        if (response.responseSuccess()) {
            return response.getKnowledgeAuthDTOS().stream().collect(Collectors.toMap(KnowledgeAuthDTO::getId, KnowledgeAuthDTO::isAuth, (o, n) -> o));
        }
        throw new RuntimeException("CRM知识批量鉴权失败, request:" + request + ", response:" + response);
    }

}
