package com.sankuai.gaigc.arrange.common.core.bot.plan;

import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Lists;
import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.UserMessage;
import com.meituan.mdp.langmodel.component.function.MdpFunctionExecutor;
import com.meituan.mdp.langmodel.component.model.chat.FridayChatLanguageModel;
import com.meituan.mdp.langmodel.component.model.chat.FridayChatLanguageModelRequester;
import com.meituan.mdp.langmodel.component.properties.model.friday.FridayModelProperties;
import com.sankuai.gaigc.arrange.common.core.bot.AIBot;
import com.sankuai.gaigc.arrange.common.core.bot.ExecuteResult;
import com.sankuai.gaigc.arrange.common.core.bot.Function;
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder;
import com.sankuai.gaigc.arrange.common.core.bot.constants.ModelConstants;
import com.sankuai.gaigc.arrange.common.core.bot.constants.PromptConst;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.FridayModelConfig;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botknowledge.BotKnowledgeRecallResult;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam;
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult;
import com.sankuai.gaigc.arrange.common.core.bot.dto.stepparam.ExecutableLLMParam;
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum;
import com.sankuai.gaigc.arrange.common.core.bot.executable.IExecutable;
import com.sankuai.gaigc.arrange.common.core.bot.executable.base.ExecutableKnowledgeRecall;
import com.sankuai.gaigc.arrange.common.core.bot.utils.FunctionNameUtil;
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils;
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ComponentConstants;
import com.sankuai.gaigc.arrange.common.enums.AgentModeEnum;
import com.sankuai.gaigc.arrange.common.model.SSEMessage;
import com.sankuai.gaigc.arrange.common.util.ApplicationContextUtil;
import com.sankuai.gaigc.arrange.config.MccConfig;
import com.sankuai.gaigc.arrange.dao.dal.dto.function.ParamDefineModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * date 2024/4/10
 */
// Planner 类
@Slf4j
@Component
public class AiPlannerService extends AbstractAgent {
    private static final int MAX_ITERATE_ROUNDS = 3;
    @Resource
    private IExecutable executableKnowledgeRecall;

    @Override
    public AgentModeEnum mode() {
        return AgentModeEnum.PLAN_AND_EXECUTE;
    }

    @Override
    public ExecutePlan makePlan(BotRunParam runParam, AIBot bot) {
        String query = runParam.getUserMsg();

        // 知识库召回
        String retrieveResult = knowledgeBaseRetrieve(runParam, bot);

        List<Function> functions = Lists.newArrayList();
        bot.getPlugins().forEach(plugin -> functions.addAll(plugin.getFunctionList()));
        // 分析查询并创建执行计划
        ExecutePlan plan = new ExecutePlan();
        try {
            String toolStr = PromptConst.PLAN_GEN_NO_TOOL_PROMPT_TEMPLATE;
            if (CollectionUtils.isNotEmpty(functions)) {
                String toolDesc = functions.stream()
                        .map(f -> "工具名:" + FunctionNameUtil.executeFuncName(f) + " 工具描述:" + f.getDescription() + " 必须有的参数：" + f.getParamInfo().getParams().stream().map(ParamDefineModel::getIdentity)
                                .collect(Collectors.joining(",")))
                        .collect(Collectors.joining("；"));
                toolStr = PromptConst.PLAN_GEN_TOOL_PROMPT_TEMPLATE.replace("{tools}", toolDesc);
            }
            // 分析查询并创建执行计划
            String characterPrompt = Objects.nonNull(bot.getCharacterPrompt()) ? bot.getCharacterPrompt() : "";
            String planPrompt = PromptConst.PLAN_GEN_PROMPT_JSON_ONLY.replace("{bot_name}", bot.getName())
                    .replace("{bot_character_prompt}", characterPrompt.trim())
                    .replace("{query}", query)
                    .replace("{toolStr}", toolStr)
                    .replace("{round}", String.valueOf(MAX_ITERATE_ROUNDS))
                    .replace("{historyMessages}", JSONObject.toJSONString(runParam.getHistoryMessages()));
            log.info("[AIBOT] plan generate prompt = {}", planPrompt);
            String generatePlanByLLM;
            if (CollectionUtils.isEmpty(functions)) {
                // 没有工具时,直接调用大模型生成回答
                generatePlanByLLM = PromptConst.PLANNING_ONLY_LLM_PROMPT.replace("{user_problem}", query);
            } else {
                // 有工具时,使用planPrompt生成执行计划
                generatePlanByLLM = generatePlanByLLM(planPrompt, bot.getModelConfig().getBotPlanModel());
            }
            log.info("[AIBOT] ai planer plans = {}", generatePlanByLLM);
            int firstIndex = generatePlanByLLM.indexOf('{');
            int lastIndex = generatePlanByLLM.lastIndexOf('}');
            JSONObject planJson = JSONObject.parseObject(generatePlanByLLM.substring(firstIndex, lastIndex + 1));
            //大模型生成plan时的返回的plan目标
            String objective = planJson.getString("objective");
            //大模型生成plan时的返回的plan步骤
            String planStepStr = planJson.getString("plan");
            List<ExecuteStep> executeSteps = JSONObject.parseArray(planStepStr, ExecuteStep.class);
            //删除大模型生产的不存在的工具
            List<String> executeFuncNames = functions.stream().map(FunctionNameUtil::executeFuncName).collect(Collectors.toList());
            executeSteps.removeIf(step -> "tool".equals(step.getType()) && !executeFuncNames.contains(step.getExecuteTool()));

            for (ExecuteStep step : executeSteps) {
                if (!StringUtils.isBlank(step.getType()) && step.getType().equals(BotExecutorTypeEnum.ANSWER.getType())) {
                    ExecutableLLMParam executableLLMParam = new ExecutableLLMParam();
                    //模型参数
                    Optional.ofNullable(bot.getModelConfig()).ifPresent(model -> {
                        executableLLMParam.setAppId(model.getAppId());
                        executableLLMParam.setFridayModelConfig(model.getBotPlanModel());
                    });

                    //知识库结果填充到prompt
                    if (StringUtils.isNotEmpty(retrieveResult)) {
                        executableLLMParam.setFillPromptFunction((curStep, planContext) -> {
                            StringBuilder botPrompt = new StringBuilder(planContext.getBot().getCharacterPrompt())
                                    .append("\n本次知识库根据用户输入召回的结果是：").append(retrieveResult);

                            String ctx = planContext.finishStepInfo(curStep);
                            if (StringUtils.isNotEmpty(ctx)) {
                                botPrompt.append("\n").append("以下是一些上下文信息，请你理解这些信息，并结合这些信息来进行回答 ：\n")
                                        .append(ctx).append("\n")
                                        .append("=====================\n");
                            }
                            if (StringUtils.isNotEmpty(curStep.getObjective())) {
                                botPrompt.append("\n")
                                        .append("本次回答的主要目标是：").append(curStep.getObjective()).append("\n")
                                        .append("=====================\n");
                            }

                            /**
                             * 2025/01/21
                             * 参数表达式jsonpath解析已优化，无需替换"#"
                             * return botPrompt.toString().replace("#", "");
                             */
                            return botPrompt.toString();
                        });
                    }

                    step.setCustomParam(executableLLMParam);
                }
            }
            plan.setObjective(objective);
            for (ExecuteStep executeStep : executeSteps) {
                ExecutionStep executionStep = new ExecutionStep();
                executionStep.getSteps().add(executeStep);
                plan.getExecutionSteps().add(executionStep);
            }

            return plan;
        } catch (Exception e) {
            log.error("[AIBOT] create plan for query = {} error", query, e);
            return fallbackPlan(runParam, e);
        }
    }

    @Override
    public BotRunResult parseResult(ExecutePlan executePlan, BotRunParam runParam, AIBot bot) {
        // 解析结果
        return parseLastStepIsLlmResult(executePlan, bot);
    }

    private String knowledgeBaseRetrieve(BotRunParam runParam, AIBot bot){
        if (CollectionUtils.isEmpty(bot.getKnowledgeBases())) {
            return null;
        }
        ExecutePlanContext planContext = ExecutePlanContext.builder()
                .runParam(runParam)
                .bot(bot).build();
        ExecuteStep executeKnowledgeRecall = new ExecuteStep();
        try {
            ExecuteResult executeResult = executableKnowledgeRecall.execute(executeKnowledgeRecall, planContext);
            BotKnowledgeRecallResult recallResult = (BotKnowledgeRecallResult) executeResult.getResult().get(ExecutableKnowledgeRecall.DATA_FIELD);
            if(recallResult != null && recallResult.getPlaceHolderAndContent() != null && recallResult.getPlaceHolderAndContent().containsKey("knowledge")){
                return recallResult.getPlaceHolderAndContent().get("knowledge");
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("智能规划与执行-知识库召回失败，botId:{}", bot.getId(), e);
        }
        return null;
    }

    @VisibleForTesting
    public String generatePlanByLLM(String planPrompt, FridayModelConfig modelConfig) {
        String modelName = ApplicationContextUtil.getBean(MccConfig.class).getBotPlanModelName();
        String appId = ComponentConstants.DEFAULT_APP_ID;

        // max_tokens的最大值不能超过模型上下文长度
        Integer maxTokens = ModelConstants.MAX_TOKENS;
        Double temperature = ModelConstants.TEMPERATURE;
        Double topP = ModelConstants.TOP_P;
        Double presencePenalty = ModelConstants.PRESENCE_PENALTY;
        Double frequencyPenalty = ModelConstants.FREQUENCY_PENALTY;

        FridayModelProperties properties = new FridayModelProperties();
        properties.setModel(modelName);
        properties.setMaxTokens(maxTokens);
        properties.setTemperature(temperature);
        properties.setTopP(topP);
        properties.setMaxRetry(3);
        properties.setPresencePenalty(presencePenalty);
        properties.setFrequencyPenalty(frequencyPenalty);
        properties.setTimeout(0);
        properties.setDebug(false);
        properties.setKeepTestAndSwimlaneUrl(false);

        Optional.ofNullable(modelConfig).ifPresent(config -> {
            Optional.ofNullable(config.getModelName()).ifPresent(properties::setModel);
            Optional.ofNullable(config.getTopP()).ifPresent(properties::setTopP);
            Optional.ofNullable(config.getMaxTokens()).ifPresent(properties::setMaxTokens);
            Optional.ofNullable(config.getTemperature()).ifPresent(properties::setTemperature);
            Optional.ofNullable(config.getFrequencyPenalty()).ifPresent(properties::setFrequencyPenalty);
            Optional.ofNullable(config.getPresencePenalty()).ifPresent(properties::setPresencePenalty);
        });

        UserMessage userMessage = UserMessage.from(planPrompt);
        FridayChatLanguageModel languageModel = new FridayChatLanguageModel(appId, new FridayChatLanguageModelRequester(), properties, new MdpFunctionExecutor());
        AssistantMessage assistantMessage = languageModel.sendMessages(Lists.newArrayList(userMessage));
        String content = assistantMessage.getContent();
        log.info("[AIBOT] generatePlanByLLM " + content);
        return content;
    }

    private ExecutePlan fallbackPlan(BotRunParam runParam, Exception e) {
        String reply = "生成执行计划错误：" + e.getMessage();
        //兜底输出错误信息
        if (runParam.getStream() && StreamInfoHolder.get() != null) {
            try {
                SSEMessage sseMessage = SSEMessageUtils.generateDirectAnswerMessage(reply, "-1", true);
                StreamInfoHolder.getEmitter().send(sseMessage);
            } catch (IOException e1) {
                log.error("流式输出出错，modelReplay=" + reply, e1);
            }
        }
        ExecutePlan plan = new ExecutePlan();
        plan.setMakePlanErrorMessage(e.getMessage());
        return plan;
    }
}
