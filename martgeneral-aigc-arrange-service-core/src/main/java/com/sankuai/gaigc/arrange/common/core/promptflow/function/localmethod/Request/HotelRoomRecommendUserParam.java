package com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod.Request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

/**
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @created: 2024/7/4
 * @description:
 */
@Data
public class HotelRoomRecommendUserParam {
    @FieldDoc(
            description = "当用户为美团时：为美团userid，当用户为点评时：传入美团虚id"
    )
    private Long userId;
    @FieldDoc(
            description = "uuid"
    )
    private String uuid;
    @FieldDoc(
            description = "用户选择城市"
    )
    private Integer userCityId;
    @FieldDoc(
            description = "定位城市"
    )
    private int gpsCityId;
    @FieldDoc(
            description = "是否开启定位 0：定位功能关闭、不能获取到定位 1：定位功能开启、可以获取到定位"
    )
    private Integer customGpsStatus;
    @FieldDoc(
            description = "纬度"
    )
    private double lat;
    @FieldDoc(
            description = "经度"
    )
    private double lng;
}
