package com.sankuai.gaigc.arrange.common.core.promptflow.component.comp;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.StringUtils;
import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ComponentTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.api.enums.VersionEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.InputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.OutputParamDefinition;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.Param;
import com.sankuai.gaigc.arrange.common.core.promptflow.annotation.PromptFlowComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.AbstractComponent;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;

import javax.annotation.Resource;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.sankuai.gaigc.arrange.common.core.promptflow.constants.ElasticSearchConstants.*;

@PromptFlowComponent(name = "ElasticSearchModify", desc = "ES数据修改组件", type = ComponentTypeEnum.ELASTIC_SEARCH_MODIFY, version = VersionEnum.COMPONENT_V1)
@InputParamDefinition({
        @Param(name = "modifyType", desc = "修改类型(INSERT:插入,UPDATE:更新,DELETE:删除)", type = ParamTypeEnum.STRING, category = CategoryEnum.COMMON_OPTION, required = true),
        @Param(name = "index", desc = "ES索引", type = ParamTypeEnum.STRING, category = CategoryEnum.DEFAULT, required = true),
        @Param(name = "keyIds", desc = "主键Ids(JSON-List<String>格式)" +
                "【INSERT: keyIds选填。如果不指定或者或者keyIds的数量不足时，系统将自动生成。" +
                "UPDATE: keyIds必填，否则更新任务不执行。" +
                "DELETE: keyIds必填，否则删除任务不执行。】", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = false),
        @Param(name = "inputData", desc = "更新数据(JSON-List<Map>格式)" +
                "【INSERT: inputData必填，根据inputData数据数量相应插入ES数据库。" +
                "UPDATE: inputData必填，否则更新任务不执行(inputData为待修改的局部信息)。" +
                "DELETE: inputData不填，删除任务仅需要主键Id。】", type = ParamTypeEnum.LIST, category = CategoryEnum.DEFAULT, required = false),
})
@OutputParamDefinition({
        @Param(name = "affectedCount", desc = "本次修改影响数据数量", type = ParamTypeEnum.INT, category = CategoryEnum.DEFAULT, required = false)}
)
@Slf4j
public class ElasticSearchModify extends AbstractComponent {

    @Resource
    RestHighLevelClient restHighLevelClient;

    @Override
    public Map<String, Object> execute(Context context, ComponentInfo componentInfo) throws Exception {
        String modifyType = ((String) parseParam("modifyType", componentInfo)).toUpperCase();
        String index = (String)parseParam("index", componentInfo);
        List inputData = (List)parseParam("inputData", componentInfo);
        List keyIds = (List)parseParam("keyIds", componentInfo);
        Map<String,Object> result = new HashMap<>();

        switch (modifyType) {
            case INSERT:
                result.put("affectedCount", (int)insertData(index, keyIds, inputData));
                break;
            case UPDATE:
                result.put("affectedCount", (int)updateData(index, keyIds, inputData));
                break;
            case DELETE:
                result.put("affectedCount", (int)deleteData(index, keyIds));
                break;
        }

        return result;
    }



    private long insertData(String index, List<String> keyIds, List<Map> inputData) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();

        for (int i = 0; i < inputData.size(); i++) {
            IndexRequest indexRequest = new IndexRequest(index);
            try {
                indexRequest.source((Map<String, Object>) inputData.get(i), XContentType.JSON);
                if(CollectionUtils.isNotEmpty(keyIds) && keyIds.size() > i) {
                    indexRequest.id(keyIds.get(i));
                }
            } catch (Exception e) {
                log.error("Error occurred while inserting data. eMessage:{} input:{}", e.getMessage(), JSONObject.toJSONString(inputData.get(i)));
                continue;
            }
            bulkRequest.add(indexRequest);
        }

        BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);

        return Arrays.stream(bulk.getItems())
                .filter(response -> {
                    if (response.getFailure() != null) {
                        // 打印log日志
                        log.error("Bulk request failed: {}", response.getFailure().getMessage());
                        return false;
                    }
                    return response.getResponse().getResult() == DocWriteResponse.Result.CREATED
                            || response.getResponse().getResult() == DocWriteResponse.Result.UPDATED;
                }).count();
    }

    private long updateData(String index, List<String> keyIds, List<Map> inputData) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();

        for (int i = 0; i < keyIds.size(); i++) {
            //如果更新数据为空或者更新数据不足则跳出
            if(CollectionUtils.isEmpty(inputData) || inputData.size() <= i) break;
            if(StringUtils.isBlank(keyIds.get(i)) || !(inputData.get(i) instanceof Map)){
                log.error("第{}个参数传输错误 index:{} input:{}", i, JSONObject.toJSONString(keyIds.get(i)), JSONObject.toJSONString(inputData.get(i)));
                continue;
            }

            UpdateRequest updateRequest = new UpdateRequest().id(keyIds.get(i)).index(index);
            try {
                updateRequest.doc(inputData.get(i));
                bulkRequest.add(updateRequest);
            } catch (Exception e) {
                log.error("Error occurred while update data. eMessage:{} input:{}", e.getMessage(), JSONObject.toJSONString(inputData.get(i)));
            }
        }

        BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);

        return Arrays.stream(bulk.getItems())
                .filter(response -> {
                    if (response.getFailure() != null) {
                        // 打印log日志
                        log.error("Bulk request failed: {}", response.getFailure().getMessage());
                        return false;
                    }
                    return response.getResponse().getResult() == DocWriteResponse.Result.CREATED
                            || response.getResponse().getResult() == DocWriteResponse.Result.UPDATED;
                }).count();
    }

    private long deleteData(String index, List<String> keyIds) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();

        if(CollectionUtils.isEmpty(keyIds)) {
            log.info("keyIds为空，删除索引无需执行");
            return 0l;
        }

        keyIds.stream().filter(keyId -> !StringUtils.isBlank(keyId)).forEach(
                keyId -> bulkRequest.add(new DeleteRequest(index).id(keyId))
        );
        BulkResponse bulk = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);

        return Arrays.stream(bulk.getItems())
                .filter(response -> {
                    if (response.getFailure() != null) {
                        // 打印log日志
                        log.error("Bulk request failed: {}", response.getFailure().getMessage());
                        return false;
                    }
                    return response.getResponse().getResult() == DocWriteResponse.Result.DELETED;
                }).count();
    }
}

