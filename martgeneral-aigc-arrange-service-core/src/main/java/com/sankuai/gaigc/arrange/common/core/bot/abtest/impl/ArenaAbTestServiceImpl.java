package com.sankuai.gaigc.arrange.common.core.bot.abtest.impl;

import com.alibaba.fastjson.JSON;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.ArenaAbTestService;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.BotAbTestRequest;
import com.sankuai.gaigc.arrange.common.core.bot.abtest.BotAbTestResponse;
import com.sankuai.gaigc.arrange.dao.dal.entity.BotAbtestInfoDO;
import com.sankuai.gaigc.arrange.dao.dal.example.BotAbtestInfoDOExample;
import com.sankuai.gaigc.arrange.dao.dal.mapper.BotAbtestInfoDOMapper;
import com.sankuai.nibaigc.flow.web.api.entity.abtestbot.ArenaABTestInfoVO;
import com.sankuai.nibaigc.flow.web.api.entity.abtestbot.ArenaStrategyInfoVO;
import com.sankuai.service.wpt.horizon.core.ArenaClient;
import com.sankuai.service.wpt.horizon.core.enums.IdTypeEnum;
import com.sankuai.service.wpt.horizon.core.vo.ArenaRequest;
import com.sankuai.service.wpt.horizon.core.vo.ArenaResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
@Slf4j
public class ArenaAbTestServiceImpl implements ArenaAbTestService {
    @Autowired
    private ArenaClient arenaClient;
    @Autowired
    private BotAbtestInfoDOMapper botAbtestInfoDOMapper;
    @Override
    public BotAbTestResponse executeBotAbTest(BotAbTestRequest botAbTestRequest) {
        if (botAbTestRequest.getArenaAbRequest() == null) {
            return BotAbTestResponse.builder().doAbTest(false).build();
        }
        log.info("执行AB实验分流逻辑 botAbTestRequest:{}", JSON.toJSONString(botAbTestRequest));
        if (StringUtils.isEmpty(botAbTestRequest.getArenaAbRequest().getIdentification())) {
            log.error("executeAbTest 缺少必填参数identification，botId={},arenaAbRequest={}", botAbTestRequest.getBotId(), botAbTestRequest.getArenaAbRequest());
            return BotAbTestResponse.builder().doAbTest(false).build();
        }
        try {
            List<BotAbtestInfoDO> botAbtestInfoDOS = getBotAbtestInfoDOS(botAbTestRequest.getBotId());
            if (CollectionUtils.isEmpty(botAbtestInfoDOS) || botAbtestInfoDOS.size() > 1) {
                log.error("executeAbTest 未配置实验或查询到多条AB实验配置 botId={}", botAbTestRequest.getBotId());
                return BotAbTestResponse.builder().doAbTest(false).build();
            }
            //取出策略配置
            BotAbtestInfoDO botAbtestInfoDO = botAbtestInfoDOS.get(0);
            ArenaABTestInfoVO arenaABTestInfoVO = ArenaABTestInfoVO.builder()
                    .expKey(botAbtestInfoDO.getArenaExpKey())
                    .status(botAbtestInfoDO.getStatus())
                    .strategyList(JSON.parseArray(botAbtestInfoDO.getGroupConfig(), ArenaStrategyInfoVO.class))
                    .build();

            //构建Arena请求
            ArenaRequest arenaRequest = getArenaRequest(botAbTestRequest, arenaABTestInfoVO);

            List<ArenaResult> arenaResults = arenaClient.getAbTestResult(arenaRequest);
            if (CollectionUtils.isEmpty(arenaResults)) {
                log.error("executeAbTest 分流结果为空 botId={}", botAbTestRequest.getBotId());
                return BotAbTestResponse.builder().doAbTest(false).build();
            }
            ArenaResult arenaResult = arenaResults.get(0);
            log.info("arenaResults:{}", JSON.toJSONString(arenaResult));

            //根据分流结果匹配bot版本
            ArenaStrategyInfoVO arenaStrategyInfoVO = arenaABTestInfoVO.getStrategyList().stream()
                    .filter(strategy -> strategy.getStrategyKey().equals(arenaResult.getGroupKey()))
                    .findFirst()
                    .orElse(null);
            if (Objects.isNull(arenaStrategyInfoVO)) {
                log.error("[executeBotAbTest]未匹配到策略 botId={},arenaResult={}", botAbTestRequest.getBotId(), JSON.toJSONString(arenaResult));
                return BotAbTestResponse.builder().doAbTest(false).build();
            }
            return BotAbTestResponse.builder().doAbTest(true).version(arenaStrategyInfoVO.getVersion()).build();
        } catch (Exception e) {
            log.error("[ArenaAbTestService]获取botAB实验配置失败(botAbTestManageService)，botId={}", botAbTestRequest.getBotId(), e);
            return BotAbTestResponse.builder().doAbTest(false).build();
        }
    }

    private static ArenaRequest getArenaRequest(BotAbTestRequest botAbTestRequest, ArenaABTestInfoVO arenaABTestInfoVO) {
        String identification = botAbTestRequest.getArenaAbRequest().getIdentification();
        IdTypeEnum idType = IdTypeEnum.UUID;
        if (Objects.nonNull(botAbTestRequest.getArenaAbRequest().getIdType())) {
            idType = IdTypeEnum.getByCode(botAbTestRequest.getArenaAbRequest().getIdType());
        }
        Integer cityId = botAbTestRequest.getArenaAbRequest().getCityId();
        Map<String, String> extParams = botAbTestRequest.getArenaAbRequest().getExtParams();
        String versionName = botAbTestRequest.getArenaAbRequest().getVersionName();
        Map<String, String> flowTag = botAbTestRequest.getArenaAbRequest().getFlowTag();

        ArenaRequest arenaRequest = ArenaRequest.builder()
                .expKey(Collections.singletonList(arenaABTestInfoVO.getExpKey()))
                .identification(identification)
                .idType(idType)
                .build();
        if (cityId != null) {
            arenaRequest.setCityId(cityId);
        }
        if (Objects.nonNull(extParams)) {
            arenaRequest.setExtParams(extParams);
        }
        if (versionName != null) {
            arenaRequest.setVersionName(versionName);
        }
        if (Objects.nonNull(flowTag)) {
            arenaRequest.setFlowTag(flowTag);
        }
        return arenaRequest;
    }

    private List<BotAbtestInfoDO> getBotAbtestInfoDOS(Long botId) {
        BotAbtestInfoDOExample example = new BotAbtestInfoDOExample();
        example.createCriteria()
                .andBotIdEqualTo(botId)
                .andStatusIn(Collections.singletonList(2)); //2 - 实验中
        return botAbtestInfoDOMapper.selectByExample(example);
    }
}
