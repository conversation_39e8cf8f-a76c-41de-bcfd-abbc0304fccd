/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.core.promptflow.util;

import com.sankuai.solarsystem.aigc.basicapi.global.common.vecscalar.CompositeExpression;
import com.sankuai.solarsystem.aigc.basicapi.global.common.vecscalar.Expression;
import com.sankuai.solarsystem.aigc.basicapi.global.common.vecscalar.GeneralExpression;
import com.sankuai.solarsystem.aigc.basicapi.global.common.vecscalar.Relation;
import java.util.Objects;
import java.util.Set;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

/**
 * Expression2DslUtil
 *
 * <AUTHOR>
 * @created 2024/2/28
 */
public class Expression2DslUtil {

    public static QueryBuilder convertDsl(String exp, String nestFieldName, Set<String> excludeFields) {
        Expression expression = ExpressionParseUtil.parse(exp);
        ExpressionConvertResult convertResult = convertDsl(expression, nestFieldName, excludeFields);

        if (convertResult.getQuery() instanceof BoolQueryBuilder) {
            return convertResult.getQuery();
        }
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(convertResult.getQuery());
        return queryBuilder;
    }

    private static ExpressionConvertResult convertDsl(Expression expression, String nestFieldName, Set<String> excludeFields) {
        if (Objects.isNull(expression)) {
            throw new IllegalArgumentException("Expression cannot be null");
        }
        if (expression instanceof CompositeExpression) {
            return compositeExpression2Dsl((CompositeExpression) expression, nestFieldName, excludeFields);
        } else if (expression instanceof GeneralExpression) {
            GeneralExpression generalExpression = (GeneralExpression) expression;
            return generalExpression2Dsl(generalExpression, nestFieldName, excludeFields);
        }
        throw new IllegalArgumentException("unsupported milvus expression " + expression.toExp());
    }

    private static ExpressionConvertResult compositeExpression2Dsl(CompositeExpression expression, String nestFieldName, Set<String> excludeFields) {
        if (CollectionUtils.isEmpty(expression.getExpressions())) {
            throw new IllegalArgumentException("CompositeExpression must have at least one sub-expression.");
        }
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        Relation relation = expression.getRelation();
        if (Relation.AND == relation) {
            expression.getExpressions().stream().map(subExp -> convertDsl(subExp, nestFieldName, excludeFields)).forEach(result -> {
                if (result.isNone()) {
                    queryBuilder.mustNot(result.getQuery());
                } else {
                    queryBuilder.must(result.getQuery());
                }
            });
        } else if (Relation.OR == relation) {
            expression.getExpressions().stream().map(subExp -> convertDsl(subExp, nestFieldName, excludeFields)).forEach(result -> {
                if (result.isNone()) {
                    BoolQueryBuilder subQuery = new BoolQueryBuilder();
                    subQuery.mustNot(result.getQuery());
                    queryBuilder.should(subQuery);
                } else {
                    queryBuilder.should(result.getQuery());
                }
            });
        } else if (Relation.NOT == relation) {
            queryBuilder.mustNot(convertDsl(expression.getExpressions().get(0), nestFieldName, excludeFields).getQuery());
        }
        return ExpressionConvertResult.buildDefault(queryBuilder);
    }

    private static ExpressionConvertResult generalExpression2Dsl(GeneralExpression expression, String nestFieldName, Set<String> excludeFields) {
        if (isExcludeField(expression, excludeFields)) {
            return notExistIfExclude(expression, nestFieldName);
        }
        Relation relation = expression.getRelation();
        String field = StringUtils.isBlank(nestFieldName) ? expression.getField() : nestFieldName + "." + expression.getField();
        switch (relation) {
            case EQUAL:
                return ExpressionConvertResult.buildDefault(QueryBuilders.termQuery(field, expression.getValues().get(0)));
            case NOT_EQUAL:
                return ExpressionConvertResult.buildNone(QueryBuilders.termQuery(field, expression.getValues().get(0)));
            case LT:
                return ExpressionConvertResult.buildDefault(QueryBuilders.rangeQuery(field).lt(expression.getValues().get(0)));
            case LE:
                return ExpressionConvertResult.buildDefault(QueryBuilders.rangeQuery(field).lte(expression.getValues().get(0)));
            case GT:
                return ExpressionConvertResult.buildDefault(QueryBuilders.rangeQuery(field).gt(expression.getValues().get(0)));
            case GE:
                return ExpressionConvertResult.buildDefault(QueryBuilders.rangeQuery(field).gte(expression.getValues().get(0)));
            case IN:
                return ExpressionConvertResult.buildDefault(QueryBuilders.termsQuery(field, expression.getValues()));
            case NOT_IN:
                return ExpressionConvertResult.buildNone(QueryBuilders.termsQuery(field, expression.getValues()));
            case LIKE:
                return ExpressionConvertResult.buildDefault(QueryBuilders.matchQuery(field, expression.getValues().get(0).replaceAll("%", "")));
            default:
                throw new IllegalArgumentException("unsupported query method" + relation.getExp());
        }
    }

    private static ExpressionConvertResult notExistIfExclude(GeneralExpression expression, String nestFieldName) {
        String field = StringUtils.isBlank(nestFieldName) ? expression.getField() : nestFieldName + "." + expression.getField();
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.mustNot(QueryBuilders.existsQuery(field));
        return ExpressionConvertResult.buildDefault(queryBuilder);
    }

    private static boolean isExcludeField(GeneralExpression expression, Set<String> excludeFields) {
        return CollectionUtils.isNotEmpty(excludeFields) && excludeFields.contains(expression.getField());
    }

    @Data
    public static class ExpressionConvertResult {
        /** 是否取非 */
        private boolean none;
        /** 查询条件 */
        private QueryBuilder query;

        public static ExpressionConvertResult buildDefault(QueryBuilder query) {
            ExpressionConvertResult result = new ExpressionConvertResult();
            result.setQuery(query);
            return result;
        }

        public static ExpressionConvertResult buildNone(QueryBuilder query) {
            ExpressionConvertResult result = new ExpressionConvertResult();
            result.setNone(true);
            result.setQuery(query);
            return result;
        }
    }
}
