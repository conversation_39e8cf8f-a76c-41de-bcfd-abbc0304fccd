package com.sankuai.gaigc.arrange.common.core.promptflow.function.remotemethod;

import com.alibaba.fastjson.JSON;
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ComponentConstants;
import com.sankuai.gaigc.arrange.common.core.promptflow.function.localmethod.entity.SearchResponse;
import com.sankuai.gaigc.arrange.common.util.HttpUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/6 14:25
 */
@Service
public class SearchHttpService {
    private static final String FRIDAY_SEARCH_API = "https://aigc.sankuai.com/v1/friday/api/search";


    public SearchResponse querySearch(String query, String channel) throws Exception {
        Map<String, String> header = new HashMap<>();
        header.put("Authorization", "Bearer " + ComponentConstants.DEFAULT_APP_ID);
        header.put("Content-Type", "application/json");

        Map<String,String> data = new HashMap<>();
        data.put("query",query);
        data.put("api",channel);

        return JSON.parseObject(HttpUtils.getPostResponse(FRIDAY_SEARCH_API, JSON.toJSONString(data), header), SearchResponse.class);
    }
}
