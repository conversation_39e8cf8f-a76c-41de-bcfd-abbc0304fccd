package com.sankuai.gaigc.arrange.common.core.promptflow.aspect;

import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ComponentAspectTest {

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getTruncatedInputs方法，输入参数为空
     */
    @Test
    public void testGetTruncatedInputsEmptyInput() {
        Map<String, ComponentParam> inputs = new HashMap<>();
        Map<String, Object> result = ComponentAspect.getTruncatedInputs(inputs);
        Assert.assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试getTruncatedInputs方法，输入参数为非String且非Map类型
     */
    @Test
    public void testGetTruncatedInputsNonStringNonMap() {
        Map<String, ComponentParam> inputs = new HashMap<>();
        inputs.put("key", new ComponentParam("name", ParamTypeEnum.INT, 123, false, "desc", CategoryEnum.DEFAULT));
        Map<String, Object> result = ComponentAspect.getTruncatedInputs(inputs);
        Assert.assertEquals("结果应包含一个整数值", 123, result.get("key"));
    }

    /**
     * 测试getTruncatedInputs方法，输入参数为String类型且超长
     */
    @Test
    public void testGetTruncatedInputsLongString() {
        Map<String, ComponentParam> inputs = new HashMap<>();
        char[] chars = new char[ComponentAspect.TEXT_MAX_LENGTH + 1];
        Arrays.fill(chars, 'a');
        String longString = new String(chars);
        inputs.put("key", new ComponentParam("name", ParamTypeEnum.STRING, longString, false, "desc", CategoryEnum.DEFAULT));
        Map<String, Object> result = ComponentAspect.getTruncatedInputs(inputs);
        Assert.assertTrue("结果字符串应被截断", ((String) result.get("key")).endsWith("...(超长省略)"));
    }

    /**
     * 测试getTruncatedInputs方法，输入参数为Map类型且超长
     */
    @Test
    public void testGetTruncatedInputsLongMap() {
        Map<String, Object> longMap = new HashMap<>();
        char[] chars = new char[ComponentAspect.TEXT_MAX_LENGTH + 1];
        Arrays.fill(chars, 'a');
        String longString = new String(chars);
        longMap.put("longKey", longString);
        Map<String, ComponentParam> inputs = new HashMap<>();
        inputs.put("key", new ComponentParam("name", ParamTypeEnum.MAP, longMap, false, "desc", CategoryEnum.DEFAULT));
        Map<String, Object> result = ComponentAspect.getTruncatedInputs(inputs);
        Map<String, Object> resultMap = (Map<String, Object>) result.get("key");
        Assert.assertTrue("结果Map中的字符串应被截断", ((String) resultMap.get("longKey")).endsWith("...(超长省略)"));
    }

    /**
     * 测试getTruncatedInputs方法，输入参数为String类型但不超长
     */
    @Test
    public void testGetTruncatedInputsShortString() {
        Map<String, ComponentParam> inputs = new HashMap<>();
        String shortString = "short";
        inputs.put("key", new ComponentParam("name", ParamTypeEnum.STRING, shortString, false, "desc", CategoryEnum.DEFAULT));
        Map<String, Object> result = ComponentAspect.getTruncatedInputs(inputs);
        Assert.assertEquals("结果应为原字符串", "short", result.get("key"));
    }

    /**
     * 测试getTruncatedInputs方法，输入参数包含空键
     */
    @Test
    public void testGetTruncatedInputsWithBlankKey() {
        Map<String, ComponentParam> inputs = new HashMap<>();
        inputs.put("", new ComponentParam("name", ParamTypeEnum.STRING, "value", false, "desc", CategoryEnum.DEFAULT));
        Map<String, Object> result = ComponentAspect.getTruncatedInputs(inputs);
        Assert.assertTrue("结果应为空", result.isEmpty());
    }
}
