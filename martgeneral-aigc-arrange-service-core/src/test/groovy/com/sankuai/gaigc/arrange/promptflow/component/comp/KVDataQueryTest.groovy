package com.sankuai.gaigc.arrange.promptflow.component.comp

import com.sankuai.gaigc.arrange.api.context.Context
import com.sankuai.gaigc.arrange.api.entity.Response
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum
import com.sankuai.gaigc.arrange.api.enums.FlowRunModeEnum
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum
import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.KVDataQuery
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentParam
import com.sankuai.gaigc.arrange.dao.dal.external.cellar.ICellarRepository
import spock.lang.Specification
import spock.lang.Unroll

/**
 * @author: zhangjiahui
 * @created: 2024/8/6
 * @description:
 */
class KVDataQueryTest extends Specification{
    KVDataQuery kvDataQuery = new KVDataQuery()
    ICellarRepository cellarRepository = Mock(ICellarRepository)

    def setup() {
        kvDataQuery.cellarRepository = cellarRepository
    }

    @Unroll
    def "测试数据查询"() {
        given: "模拟输入参数"
        Context context = new Context()
        context.setRunMode(FlowRunModeEnum.SYNC)
        ComponentInfo componentInfo = new ComponentInfo()
        componentInfo.setInputs(
                "queryPrefix"  : new ComponentParam("queryPrefix", ParamTypeEnum.STRING, "prefix", false, "数据查询前缀信息", CategoryEnum.CUSTOM),
                "queryKey" : new ComponentParam("queryKey", ParamTypeEnum.STRING, "key", false, "数据查询查询条件", CategoryEnum.CUSTOM),
                )
        cellarRepository.cellarCacheComponentRead(_, _) >> "success"

        when: "执行方法"
        Map<String, Object> result = kvDataQuery.execute(context, componentInfo)

        then: "验证结果"
        result.data == "success"
    }

    @Unroll
    def "测试批量数据查询"() {
        given: "模拟输入参数"
        Context context = new Context()
        context.setRunMode(FlowRunModeEnum.SYNC)
        ComponentInfo componentInfo = new ComponentInfo()
        componentInfo.setInputs(
                "queryPrefix"  : new ComponentParam("queryPrefix", ParamTypeEnum.STRING, "prefix", false, "数据查询前缀信息", CategoryEnum.CUSTOM),
                "batchQueryKey" : new ComponentParam("batchQueryKey", ParamTypeEnum.LIST, ["key"], false, "批量数据查询查询条件", CategoryEnum.CUSTOM),
        )
        cellarRepository.cellarCacheComponentRead(_, _) >> "success"

        when: "执行方法"
        Map<String, Object> result = kvDataQuery.execute(context, componentInfo)

        then: "验证结果"
        result.batchData == ["success"]
    }
}
