package com.sankuai.gaigc.arrange.common.core.bot.utils

import com.google.common.collect.Lists
import com.sankuai.gaigc.arrange.common.constant.MessageTypeConstants
import com.sankuai.gaigc.arrange.common.core.bot.AIBot
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfo
import com.sankuai.gaigc.arrange.common.core.bot.StreamInfoHolder
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.enhance.AnswerReferenceKnowledge
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotCardResult
import com.sankuai.gaigc.arrange.common.core.bot.dto.botresult.BotRunResult
import com.sankuai.gaigc.arrange.common.model.SSEMessage
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.atomic.AtomicInteger

import static org.powermock.api.mockito.PowerMockito.mockStatic
import static org.powermock.api.mockito.PowerMockito.when

/**
 * SSEMessageUtils单元测试
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest(StreamInfoHolder.class)
class SSEMessageUtilsTest extends Specification {
    @Unroll
    def "测试 botFirstTokenMetric 当 bot 和 sseMessage 都不为 null 且 sseMessage 的 index 为 0 时"() {
        given:
        AIBot bot = Mock(AIBot)
        SSEMessage sseMessage = Mock(SSEMessage)
        bot.getStart() >> 1000L
        bot.getId() >> 1L
        sseMessage.getIndex() >> 0

        when:
        SSEMessageUtils.botFirstTokenMetric(bot, sseMessage)

        then:
        0 * SSEMessageUtils.logBotMetric(1000L, "1", "FIRST_TOKEN")
    }

    def "generateAnswerReferenceMessage"() {
        given:
        double time_cost = 0.6D
        AnswerReferenceKnowledge data1 = new AnswerReferenceKnowledge()
        data1.setKnowledgeBaseId(1L)
        data1.setKnowledgeBaseName("测试")
        data1.setKnowledgeContent("测试")
        List<AnswerReferenceKnowledge> answerReferenceKnowledge = Lists.newArrayList(data1)

        mockStatic(StreamInfoHolder)
        when(StreamInfoHolder.getIndex()).thenReturn(new AtomicInteger(1))
        when:
        SSEMessage message = SSEMessageUtils.generateAnswerReferenceMessage(answerReferenceKnowledge, time_cost, "35267432467")
        then:
        message.type == MessageTypeConstants.ANSWER_REFERENCE
    }

    def "generateAnswerReferenceMessageFail"() {
        given:
        double time_cost = 0.6D
        when:
        SSEMessage message = SSEMessageUtils.generateAnswerReferenceMessage(Collections.emptyList(), time_cost, "35267432467")
        then:
        message == null
    }

    def "generateQueryRecommendMessage"() {
        given:
        double time_cost = 0.6D
        List<String> contents = Lists.newArrayList("测试")
        mockStatic(StreamInfoHolder)
        when(StreamInfoHolder.getIndex()).thenReturn(new AtomicInteger(1))
        when:
        SSEMessage message = SSEMessageUtils.generateQueryRecommendMessage(contents, time_cost, "35267432467")
        then:
        message.getContent() == "[\"测试\"]"
    }

    def "generateQueryRecommendMessageFail"() {
        given:
        double time_cost = 0.6D
        when:
        SSEMessage message = SSEMessageUtils.generateQueryRecommendMessage(Collections.emptyList(), time_cost, "35267432467")
        then:
        message == null
    }


    @Unroll
    def "测试 generateBotCardResult 正常情况下返回正确的 SSEMessage"() {
        given:
        BotCardResult botCardResult = new BotCardResult()
        double time_cost = 1.5
        String section_id = "section123"
        mockStatic(StreamInfoHolder)
        when(StreamInfoHolder.get()).thenReturn(new StreamInfo(new SseEmitter(1000),
                new AtomicInteger(0),
                UUID.randomUUID().toString()))
        when(StreamInfoHolder.getIndex()).thenReturn(new AtomicInteger(1))


        when:
        SSEMessage result = SSEMessageUtils.generateBotCardResult(botCardResult, time_cost, section_id)

        then:
        result != null
        result.index == 1
        result.role == "assistant"
        result.type == "card_reply"
    }


    @Unroll
    def "generateBotResult should return SSEMessage object with correct values under normal conditions"() {

        given:
        BotRunResult botRunResult = new BotRunResult(true, null, "Test Message")
        double time_cost = 2.5
        mockStatic(StreamInfoHolder)
        when(StreamInfoHolder.get()).thenReturn(new StreamInfo(new SseEmitter(1000),
                new AtomicInteger(0),
                UUID.randomUUID().toString()))
        when(StreamInfoHolder.getIndex()).thenReturn(new AtomicInteger(1))

        when:
        SSEMessage result = SSEMessageUtils.generateBotResult(botRunResult, time_cost)

        then:
        result != null
        result.index == 1
        result.role == "assistant"
        result.type == "bot_result"
    }

}
