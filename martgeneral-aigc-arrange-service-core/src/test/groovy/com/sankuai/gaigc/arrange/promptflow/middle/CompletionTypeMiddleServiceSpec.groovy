/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.promptflow.middle

import com.meituan.mdp.langmodel.api.message.AssistantMessage
import com.meituan.mdp.langmodel.api.message.FuncExecutionResultMessage
import com.meituan.mdp.langmodel.api.message.Message
import com.meituan.mdp.langmodel.api.message.UserMessage
import com.meituan.mdp.langmodel.component.properties.model.friday.FridayModelProperties
import com.sankuai.gaigc.arrange.common.core.friday.FuxiChatModel
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto.CompletionTypeParam
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto.GeneralLLMComponentParam
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto.GeneralLLMRequestParam
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.dto.LLMChatMessageData
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.service.impl.CompletionTypeMiddleService
import com.sankuai.gaigc.arrange.dao.dal.dto.function.FunctionSerializeModel
import spock.lang.Specification
import spock.lang.Unroll
/**
 * 在这里编写类的功能描述
 *
 * <AUTHOR>
 * @created 2024/12/19
 */
class CompletionTypeMiddleServiceSpec extends Specification {
    CompletionTypeMiddleService service = new CompletionTypeMiddleService()
    FuxiChatModel chatLanguageModel = Mock()

    def setup() {
        service.mockModel = chatLanguageModel
    }

    @Unroll
    def "测试convertResultFunctions非空时的行为"() {
        given: "模拟输入参数和预期的行为"
        GeneralLLMRequestParam requestParam = Mock(CompletionTypeParam)
        GeneralLLMComponentParam componentParam = new GeneralLLMComponentParam()
        componentParam.setAppId("testAppId")

        FunctionSerializeModel functionSerializeModel = new FunctionSerializeModel()
        functionSerializeModel.setName("test")
        functionSerializeModel.setDescription("测试")
        componentParam.convertResultFunctions = [functionSerializeModel]
        LLMChatMessageData chatMessageData = LLMChatMessageData.build()
        Message userMessage = new UserMessage("测试")
        chatMessageData.setUserMessage(userMessage)
        FuncExecutionResultMessage funcExecutionResultMessage = new FuncExecutionResultMessage("test", "111")
        chatMessageData.setFuncExecutionResultMessage(funcExecutionResultMessage)
        FridayModelProperties fridayModelProperties = new FridayModelProperties()
        requestParam.getComponentParam() >> componentParam
        requestParam.getChatMessageData() >> chatMessageData
        requestParam.getFridayModelProperties() >> fridayModelProperties

        when: "调用doRequest方法"
        service.doRequest(requestParam)

        then: "验证sendMessagesUseCustomFunc方法被调用"
        1 * chatLanguageModel.sendMessagesUseCustomFunc(_, _) >> Mock(AssistantMessage)
    }

    @Unroll
    def "测试convertResultFunctions为空时的行为"() {
        given: "模拟输入参数和预期的行为"
        GeneralLLMRequestParam requestParam = Mock(CompletionTypeParam)
        GeneralLLMComponentParam componentParam = new GeneralLLMComponentParam()
        componentParam.setAppId("21432431431")
        LLMChatMessageData chatMessageData = LLMChatMessageData.build()
        Message userMessage = new UserMessage("测试")
        chatMessageData.setUserMessage(userMessage)
        FridayModelProperties fridayModelProperties = new FridayModelProperties()
        requestParam.getComponentParam() >> componentParam
        requestParam.getChatMessageData() >> chatMessageData
        requestParam.getFridayModelProperties() >> fridayModelProperties

        when: "调用doRequest方法"
        service.doRequest(requestParam)

        then: "验证sendVisionMessage方法被调用"
        1 * chatLanguageModel.sendVisionMessage(_) >> Mock(AssistantMessage)
    }
}