package com.sankuai.gaigc.arrange.common.core.util.baoshijie


import com.sankuai.gaigc.arrange.common.core.promptflow.remote.baoshijie.BaoShiJieViolationInfoDTO
import org.apache.commons.lang3.time.DateFormatUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import spock.lang.Specification

class BaoShiJieViolationInfoDTOSpec extends Specification {
    private static final Logger log = LoggerFactory.getLogger(this);

    def "BaoShiJieViolationInfoDTOFrom"() {
        given:
        false
        when:
        Map<String, Object> source = new HashMap<>()
        source.put("confidence", 4)
        source.put("name", "shopName")
        source.put("statusCode", "3")
        source.put("delReason", "涉黄")
        source.put("delReason", "涉黄")
        source.put("verifyDesc", "涉黄")
        source.put("subStatusCode", 75)
        source.put("subVerifyDesc", "色情")
        source.put("hitKey", "法轮毂")
        source.put("advice", 2)
        source.put("degree", 3)
        BaoShiJieViolationInfoDTO target = BaoShiJieViolationInfoDTO.from(source)
        target.getBaoShiJieStatusNameListStatusCodeEnum();
        String verifyDesc = target.getVerifyDesc();
        String subVerifyDesc = target.getSubVerifyDesc();
        String hitKey = target.getHitKey();

        log.info("desc: {}", target.getViolationDesc())

        source.put("hitKey", null)
        BaoShiJieViolationInfoDTO target2 = BaoShiJieViolationInfoDTO.from(source)

        log.info("hitKey is null desc: {}", target2.getViolationDesc())
        then:
        target != null
    }


    def DateFormatUtilsFormat() {
        given:
        true
        when:
        def format = DateFormatUtils.format(Calendar.getInstance(), "yyyy年MM月dd日")

        def format1 = DateFormatUtils.format(Calendar.getInstance(), "yyyy年MM月dd日HH时mm分ss秒")
        log.info("format1: {}", format1)
        log.info("format: {}", format)
        then:
        format != null

    }
}
