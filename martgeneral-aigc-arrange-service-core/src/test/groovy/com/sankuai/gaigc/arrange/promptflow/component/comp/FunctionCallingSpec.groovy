package com.sankuai.gaigc.arrange.promptflow.component.comp

import com.alibaba.fastjson.JSONObject
import com.dianping.rhino.circuit.CircuitBreaker
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.reflect.TypeToken
import com.meituan.mafka.client.producer.IProducerProcessor
import com.meituan.mafka.client.producer.ProducerResult
import com.meituan.mafka.client.producer.ProducerStatus
import com.meituan.mdp.langmodel.api.message.AssistantMessage
import com.meituan.mdp.langmodel.api.message.FunctionCallResponse
import com.meituan.mdp.langmodel.api.message.MdpUsage
import com.meituan.mdp.langmodel.component.model.chat.OpenAIChatModel
import com.meituan.mdp.langmodel.component.properties.model.friday.FridayModelProperties
import com.sankuai.gaigc.arrange.api.context.Context
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum
import com.sankuai.gaigc.arrange.common.core.bot.Function
import com.sankuai.gaigc.arrange.common.core.bot.Plugin
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.BotVariableItem
import com.sankuai.gaigc.arrange.common.core.bot.service.AIBotSnapshotService
import com.sankuai.gaigc.arrange.common.core.bot.service.AiBotPluginService
import com.sankuai.gaigc.arrange.common.core.friday.FuxiChatModel
import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.FunctionCalling
import com.sankuai.gaigc.arrange.common.core.promptflow.constants.ComponentConstants
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.aigc.flow.cost.sendmsg.SendCalculateFlowCostMQMessage
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo
import com.sankuai.gaigc.arrange.common.core.promptflow.middle.service.FuxiCircuitBreaker
import com.sankuai.gaigc.arrange.common.core.promptflow.remoteproxy.IOpenAIChatModelMockService
import com.sankuai.gaigc.arrange.common.core.promptflow.remoteproxy.impl.OpenAIChatModelMockServiceImpl
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil
import com.sankuai.gaigc.arrange.dao.dal.dto.function.*
import com.sankuai.gaigc.arrange.dao.dal.enums.FunctionParamTypeEnum
import com.sankuai.gaigc.arrange.dao.dal.enums.FunctionTypeEnum
import com.sankuai.gaigc.arrange.dao.dal.mapper.AigcFunctionMapper
import org.assertj.core.util.Lists
import org.junit.Ignore
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Requires
import spock.lang.Specification
import spock.lang.Unroll

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PowerMockIgnore("javax.net.ssl.*")
@PrepareForTest([SendCalculateFlowCostMQMessage.class, FuxiChatModel.class, FunctionCalling.class])
class FunctionCallingSpec extends Specification {

    FunctionCalling functionCalling
    AigcFunctionMapper aigcFunctionMapper = Mock()
    AiBotPluginService aiBotPluginService = Mock()
    ObjectMapper objectMapper = new ObjectMapper()
    OpenAIChatModel chatLanguageModel = Mock()
    IOpenAIChatModelMockService openAIChatModelMockService = Mock(OpenAIChatModelMockServiceImpl.class)
    IProducerProcessor aigcFlowCostCollectRecordProducerMock = Mock(IProducerProcessor)
    AIBotSnapshotService aiBotSnapshotService = Mock()
    FuxiChatModel fuxiChatModel = Mock(FuxiChatModel.class)
    FuxiCircuitBreaker fuxiCircuitBreaker = Mock();
    CircuitBreaker circuitBreaker = Mock()


    def setup() {
        functionCalling = new FunctionCalling()
        functionCalling.aigcFlowCostCollectRecordProducer = aigcFlowCostCollectRecordProducerMock
        functionCalling.aigcFunctionMapper = aigcFunctionMapper
        functionCalling.aiBotPluginService = aiBotPluginService
        functionCalling.openAIChatModelMockService = openAIChatModelMockService
        functionCalling.aiBotSnapshotService = aiBotSnapshotService
        functionCalling.fuxiCircuitBreaker = fuxiCircuitBreaker

        fuxiCircuitBreaker.circuitBreakerWithModelDegrade(_) >> circuitBreaker
        circuitBreaker.allowRequest() >> true
    }

    /**
     * 测试 execute 方法，当 functions 参数为空时，应抛出 IllegalArgumentException 异常
     */
    @Unroll
    @Ignore
    @Requires({ System.getProperty("os.name").contains("Mac OS X") })
    def "test execute with flow"() {
        given:
        Context context = Mock()

        HashMap<String, Object[]> hashMap = new HashMap<String, Object[]>() {
            {
                put("forceExecuteFunction", Lists.newArrayList("true", ParamTypeEnum.BOOLEAN).toArray())
                put("openAuth", Lists.newArrayList("false", ParamTypeEnum.BOOLEAN).toArray())
                put("modelName", Lists.newArrayList("gpt-4", ParamTypeEnum.STRING).toArray())
                put("systemPrompt", Lists.newArrayList("你是一个小助手", ParamTypeEnum.STRING).toArray())
                put("userPrompt", Lists.newArrayList("hello", ParamTypeEnum.STRING).toArray())
                put("appId", Lists.newArrayList(ComponentConstants.DEFAULT_APP_ID, ParamTypeEnum.STRING).toArray())
                put("functionType", Lists.newArrayList(5, ParamTypeEnum.INT).toArray())
                put("flows", Lists.newArrayList(JSONObject.toJSONString(com.google.common.collect.Lists.newArrayList("1")), ParamTypeEnum.LIST).toArray())
            }
        }
        ComponentInfo componentInfo = new ComponentInfo()
        componentInfo.setAppId("3")
        componentInfo.setInputs(CompParamsUtil.buildInput(hashMap))
        FunctionCallInfo functionCallInfo = new WorkFlowFunctionCallInfo()
        functionCallInfo.setServiceName("com.sankuai.gaigc.arrange.common.core.promptflow.util.ThriftUtils")
        functionCallInfo.setMethodName("buildAsyncLlmateResponse")
        functionCallInfo.setIsSpringBean(false)

        ParamDefineModel flowIdParam = new ParamDefineModel()
        flowIdParam.setName("flow id")
        flowIdParam.setIdentity("flowId")
        flowIdParam.setDesc("工作流ID")
        flowIdParam.setDataType(FunctionParamTypeEnum.STRING.getValue())
        flowIdParam.setRequired(true)
        flowIdParam.setDefaultValue("1")

        List<ParamDefineModel> params = com.google.common.collect.Lists.newArrayListWithCapacity(2)
        params.add(flowIdParam)

        FunctionParamInfo paramInfo = new FunctionParamInfo()
        paramInfo.setParams(params)

        Function function = new Function()
        function.id = 1
        function.flowId = "1"
        function.name = "123"
        function.description = "desc"
        function.funcType = FunctionTypeEnum.WORK_FLOW
        function.setCallInfo(functionCallInfo)
        function.setParamInfo(paramInfo)

        Plugin plugin = new Plugin()
        plugin.functionList = [function]

        AssistantMessage res = new AssistantMessage()
        res.code = 200
        res.usage = new MdpUsage(1, 1, 1)
        res.content = "test test"
        JsonNode jsonNode = objectMapper.readTree("{\"taskId\":753}")
        res.functionCallResponse = new FunctionCallResponse("WORK_FLOW_1", jsonNode, "{\"taskId\":753}")

        chatLanguageModel.getProperties() >> new FridayModelProperties()
        functionCalling.aiBotPluginService.convertFlowToPlugin(_) >> [plugin]
        openAIChatModelMockService.newOpenAIChatModel(_) >> chatLanguageModel
        chatLanguageModel.sendMessagesUseCustomFunc(_, _) >> res >> res

        functionCalling.getFuxiChatModel(_) >> fuxiChatModel
        fuxiChatModel.sendMessagesUseCustomFunc(_, _) >> res

        SendCalculateFlowCostMQMessage create = SendCalculateFlowCostMQMessage.create(aigcFlowCostCollectRecordProducerMock)
        PowerMockito.mockStatic(SendCalculateFlowCostMQMessage.class)
        PowerMockito.when(SendCalculateFlowCostMQMessage.create(Mockito.any(IProducerProcessor.class)))
                .thenReturn(create);
        aigcFlowCostCollectRecordProducerMock.sendMessage(_) >> new ProducerResult(ProducerStatus.SEND_OK)
        when:
        Map<String, Object> result = functionCalling.execute(context, componentInfo)

        then:
        result.size() > 0
    }

    @Unroll
    def "测试resetParamDefaultValue"() {
        given:
        String configJson = "[{\"name\":\"identification\",\"type\":8,\"value\":\"4HPF1HTFDR\"}]"
        List<BotVariableItem> variableItems = GsonUtil.fromJson(configJson, new TypeToken<List<BotVariableItem>>() {
        }.getType())
        aiBotSnapshotService.getBotVariable(_) >> variableItems

        String functionJson = "[{\"id\":24,\"name\":\"knowledgeCubeRecall\",\"description\":\"知识库中台召回知识\",\"funcType\":\"MTTHRIFT\",\"callInfo\":{\"thriftRemoteAppKey\":\"com.sankuai.kcube.server\",\"thriftPort\":null,\"thriftServiceName\":\"com.sankuai.kcube.cube.thrift.service.KnowledgeCubeQueryService\",\"thriftMethodName\":\"recall\",\"timeout\":5000},\"paramInfo\":{\"params\":[{\"identity\":\"request\",\"name\":\"请求参数\",\"desc\":\"请求参数\",\"required\":true,\"dataType\":\"object\",\"defaultValue\":null,\"enumValues\":null,\"paramClass\":\"com.sankuai.kcube.cube.thrift.model.recall.RecallRequest\",\"objAttributes\":[{\"identity\":\"systemMark\",\"name\":\"接入系统标识\",\"desc\":\"接入系统标识\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"com.sankuai.gaigc.arrange.service\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"identification\",\"name\":\"应用标识\",\"desc\":\"应用标识\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"query\",\"name\":\"用户query\",\"desc\":\"用户query\",\"required\":true,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"expression\",\"name\":\"标量表达式\",\"desc\":\"标量表达式\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"userInfo\",\"name\":\"用户信息\",\"desc\":\"用户信息\",\"required\":false,\"dataType\":\"object\",\"defaultValue\":null,\"enumValues\":null,\"paramClass\":\"com.sankuai.kcube.cube.thrift.model.UserInfo\",\"objAttributes\":[{\"identity\":\"accountType\",\"name\":\"用户类型\",\"desc\":\"用户类型\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"MEITUAN_MIS\",\"enumValues\":[\"MEITUAN_MIS\",\"HOTEL_MERCHANT\"],\"paramClass\":\"com.sankuai.kcube.cube.thrift.enums.UserAccountTypeEnum\",\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"mis\",\"name\":\"美团员工mis\",\"desc\":\"美团员工mis\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null},{\"identity\":\"hotelAccount\",\"name\":\"美团酒店商家账号\",\"desc\":\"美团酒店商家账号\",\"required\":false,\"dataType\":\"string\",\"defaultValue\":\"\",\"enumValues\":null,\"paramClass\":null,\"objAttributes\":null,\"arrayElement\":null}],\"arrayElement\":null}],\"arrayElement\":null}],\"headers\":null},\"creator\":\"guoxinye02\",\"modifier\":\"guoxinye02\",\"createTime\":\"Jun 13, 2024 3:03:12 PM\",\"updateTime\":\"Jun 17, 2024 11:31:25 AM\"}]"
        List<AigcFunctionDto> functions = GsonUtil.fromJson(functionJson, new TypeToken<List<AigcFunctionDto>>() {
        }.getType())
        when:
        functionCalling.resetParamDefaultValue("418", functions)
        then:
        def functionDto = functions.get(0)
        functionDto.toString().contains("4HPF1HTFDR")
    }

}