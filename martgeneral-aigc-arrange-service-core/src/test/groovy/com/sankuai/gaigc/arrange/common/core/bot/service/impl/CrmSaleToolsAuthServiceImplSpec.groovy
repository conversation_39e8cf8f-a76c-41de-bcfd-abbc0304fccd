package com.sankuai.gaigc.arrange.common.core.bot.service.impl

import com.google.gson.reflect.TypeToken
import com.sankuai.apolloinfra.crm.saleskits.api.response.bot.KnowledgeAuthResponse
import com.sankuai.apolloinfra.crm.saleskits.api.service.bot.TAIBotKnowledgeAuthService
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.BotKnowledgeAuthConfig
import com.sankuai.gaigc.arrange.common.core.bot.dto.botconfig.extension.CrmKnowledgeAuthConfig
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.UserModel
import com.sankuai.gaigc.arrange.common.util.gson.GsonUtil
import com.sankuai.gaigc.arrange.dao.dal.dto.knowledge.KnowledgeBaseRetrieveResult
import org.apache.commons.collections4.MapUtils
import org.apache.curator.shaded.com.google.common.collect.Maps
import spock.lang.Specification

class CrmSaleToolsAuthServiceImplSpec extends Specification {

    CrmSaleToolsAuthServiceImpl crmSaleToolsAuthService
    TAIBotKnowledgeAuthService tAiBotKnowledgeAuthService

    def setup() {
        tAiBotKnowledgeAuthService = Mock()
        crmSaleToolsAuthService = new CrmSaleToolsAuthServiceImpl(tAiBotKnowledgeAuthService: tAiBotKnowledgeAuthService)
    }

    def "空知识鉴权"() {
        when:
        BotKnowledgeAuthConfig authConfig = new BotKnowledgeAuthConfig()
        CrmKnowledgeAuthConfig crmAuthConfig = new CrmKnowledgeAuthConfig()
        authConfig.setCrmAuthConfig(crmAuthConfig)
        UserModel userModel = new UserModel()
        userModel.setUserId(11L)
        Map<String, KnowledgeBaseRetrieveResult> resultMap = crmSaleToolsAuthService.authData(Maps.newHashMap(), userModel, authConfig)
        then:
        MapUtils.isEmpty(resultMap)
    }

    def "非空知识鉴权"() {
        given:
        String responseJson = "{\"knowledgeAuthDTOS\":[{\"tenantId\":2,\"id\":71076,\"channel\":\"2036479533\",\"auth\":true},{\"tenantId\":2,\"id\":71076,\"channel\":\"2036479533\",\"auth\":true},{\"tenantId\":2,\"id\":71847,\"channel\":\"2036479533\",\"auth\":true},{\"tenantId\":2,\"id\":71926,\"channel\":\"2036479533\",\"auth\":true},{\"tenantId\":2,\"id\":71847,\"channel\":\"2036479533\",\"auth\":true}],\"code\":200,\"message\":null}"
        KnowledgeAuthResponse response = GsonUtil.fromJson(responseJson, KnowledgeAuthResponse.class);
        tAiBotKnowledgeAuthService.authKnowledges(_) >> response

        String retrieveResultJson = "{\"knowledgeByQuestion\":{\"knowledgeBaseId\":8,\"knowledgeBaseName\":\"B端问答知识库\",\"result\":[{\"fieldData\":{\"docId\":71076,\"sourceId\":\"2036479533\",\"question\":\"[\\\"战区内连锁门店摘牌/挂牌，需要进行私海流转怎么办？\\\",\\\"\\\\n门店退加盟请问怎么切分出去，已经退出连锁了?\\\",\\\"\\\\n门店已摘牌，退出集团连锁，如何切割流出私海？\\\",\\\"\\\\n请问下我这边的连锁酒店被电销拿了私海，但是连锁那边只认我上?\\\"]\",\"answer\":\"[\\\"战区内连锁门店摘牌/挂牌，需要进行私海流转请按照流程申请，统一提报切分，乐享666无权限操作，辛苦理解；\\\",\\\"\\\\n\\\\n销售团队POI划分执行流程https://km.sankuai.com/collabpage/1737597927；\\\",\\\"\\\\n\\\\n对接人：周新云zhouxinyun。\\\"]\",\"appendInfo\":\"{roles=[], labels=[89331]}\"},\"maxScore\":1},{\"fieldData\":{\"docId\":71076,\"sourceId\":\"2036479533\",\"question\":\"[\\\"战区内连锁门店摘牌/挂牌，需要进行私海流转怎么办？\\\",\\\"\\\\n门店退加盟请问怎么切分出去，已经退出连锁了?\\\",\\\"\\\\n门店已摘牌，退出集团连锁，如何切割流出私海？\\\",\\\"\\\\n请问下我这边的连锁酒店被电销拿了私海，但是连锁那边只认我上?\\\"]\",\"answer\":\"[\\\"战区内连锁门店摘牌/挂牌，需要进行私海流转请按照流程申请，统一提报切分，乐享666无权限操作，辛苦理解；\\\",\\\"\\\\n\\\\n销售团队POI划分执行流程https://km.sankuai.com/collabpage/1737597927；\\\",\\\"\\\\n\\\\n对接人：周新云zhouxinyun。\\\"]\",\"appendInfo\":\"{roles=[], labels=[89331]}\"},\"maxScore\":1},{\"fieldData\":{\"docId\":71847,\"sourceId\":\"2036479533\",\"question\":\"[\\\"门店合并拆分?\\\\n申请门店及评分评价拆分?\\\",\\\"\\\\n无法拆分 门店?\\\\n问题类型：拆分门店 问题描述：酒店和之前同位置的其他倒闭酒店合并订单和评价，现在商家想要拆分?\\\",\\\"\\\\n您好，我这边有个门店新上的，然后被合并了，合并门店倒闭了，我需要拆分出来?\\\",\\\"\\\\n重复门店合并后，被合并的信息，例如评价能够不在现在的酒店中展示嘛？\\\",\\\"\\\\n现在不在我名下，网上也查不到了，显示门店合并，请问如何处理?\\\",\\\"\\\\n怎么查看门店合并的原因?\\\",\\\"\\\\n拆分商家?\\\\n这个门店被合并了，改如何拆分?\\\"]\",\"answer\":\"[\\\"【门店合并拆分】申请门店拆分问题请移步http://dpurl.cn/CerM1Lf ，举报时可提供【新店营业执照（执照地址与POI地址一致）】、【法人身份证】、【转让协议】、【门头图（基础信息与poi信息一致）】及申诉理由，未填写可能会受理失败。\\\",\\\"\\\\n\\\\n如有问题辛苦反馈【TDC小助手】大象公众号，直接点击跳转：[TDC小助手|mtdaxiang://www.meituan.com/chat?\\\",\\\"pubid=345008] 或反馈工单?\\\",\\\"???[门店举报重复/举报拆分|https://tt.sankuai.com/ticket/create?\\\",\\\"cid=80&tid=1562&iid=7075]\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.90418345},{\"fieldData\":{\"docId\":71926,\"sourceId\":\"2036479533\",\"question\":\"[\\\"门店合并拆分？\\\",\\\"\\\\n申请门店及评分评价拆分？\\\",\\\"\\\\n无法拆分 门店？\\\",\\\"\\\\n问题类型：拆分门店 问题描述：酒店和之前同位置的其他倒闭酒店合并订单和评价，现在商家想要拆分？\\\",\\\"\\\\n您好，我这边有个门店新上的，然后被合并了，合并门店倒闭了，我需要拆分出来\\\\n重复门店合并后，被合并的信息，例如评价能够不在现在的酒店中展示嘛？\\\",\\\"\\\\n现在不在我名下，网上也查不到了，显示门店合并，请问如何处理？\\\",\\\"\\\\n怎么查看门店合并的原因？\\\",\\\"\\\\n拆分商家？\\\",\\\"\\\\n这个门店被合并了，改如何拆分？\\\",\\\"\\\\n门店拆分失败？\\\"]\",\"answer\":\"[\\\"【门店合并拆分】申请门店拆分问题请移步http://dpurl.cn/CerM1Lf ，举报时可提供【新店营业执照（执照地址与POI地址一致）】、【法人身份证】、【转让协议】、【门头图（基础信息与poi信息一致）】及申诉理由，未填写可能会受理失败。\\\",\\\"\\\\n\\\\n如有问题辛苦反馈【TDC小助手】大象公众号，直接点击跳转：[TDC小助手|mtdaxiang://www.meituan.com/chat?\\\",\\\"pubid=345008] 或反馈工单?\\\",\\\"?\\\",\\\"?\\\",\\\"?\\\",\\\"[门店举报重复/举报拆分|https://tt.sankuai.com/ticket/create?\\\",\\\"cid=80&tid=1562&iid=7075]\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.90177643},{\"fieldData\":{\"docId\":71847,\"sourceId\":\"2036479533\",\"question\":\"[\\\"门店合并拆分?\\\\n申请门店及评分评价拆分?\\\",\\\"\\\\n无法拆分 门店?\\\\n问题类型：拆分门店 问题描述：酒店和之前同位置的其他倒闭酒店合并订单和评价，现在商家想要拆分?\\\",\\\"\\\\n您好，我这边有个门店新上的，然后被合并了，合并门店倒闭了，我需要拆分出来?\\\",\\\"\\\\n重复门店合并后，被合并的信息，例如评价能够不在现在的酒店中展示嘛？\\\",\\\"\\\\n现在不在我名下，网上也查不到了，显示门店合并，请问如何处理?\\\",\\\"\\\\n怎么查看门店合并的原因?\\\",\\\"\\\\n拆分商家?\\\\n这个门店被合并了，改如何拆分?\\\"]\",\"answer\":\"[\\\"【门店合并拆分】申请门店拆分问题请移步http://dpurl.cn/CerM1Lf ，举报时可提供【新店营业执照（执照地址与POI地址一致）】、【法人身份证】、【转让协议】、【门头图（基础信息与poi信息一致）】及申诉理由，未填写可能会受理失败。\\\",\\\"\\\\n\\\\n如有问题辛苦反馈【TDC小助手】大象公众号，直接点击跳转：[TDC小助手|mtdaxiang://www.meituan.com/chat?\\\",\\\"pubid=345008] 或反馈工单?\\\",\\\"???[门店举报重复/举报拆分|https://tt.sankuai.com/ticket/create?\\\",\\\"cid=80&tid=1562&iid=7075]\\\"]\",\"appendInfo\":\"{roles=[], labels=[]}\"},\"maxScore\":0.90418345}]}}"
        Map<String, KnowledgeBaseRetrieveResult> retrieveResult = GsonUtil.fromJson(retrieveResultJson, new TypeToken<Map<String, KnowledgeBaseRetrieveResult>>() {
        }.getType())

        String authConfigJson = "{\"open\":true,\"strategy\":1,\"crmAuthConfig\":{\"idFieldName\":\"docId\",\"channelFieldName\":\"sourceId\"}}"
        BotKnowledgeAuthConfig authConfig = GsonUtil.fromJson(authConfigJson, BotKnowledgeAuthConfig.class)
        when:
        UserModel userModel = new UserModel()
        userModel.setUserId(11L)
        userModel.setUserType(1)
        userModel.setUserName("guoxinye02")
        Map<String, KnowledgeBaseRetrieveResult> resultMap = crmSaleToolsAuthService.authData(retrieveResult, userModel, authConfig)
        then:
        resultMap.containsKey("knowledgeByQuestion")
    }

}
