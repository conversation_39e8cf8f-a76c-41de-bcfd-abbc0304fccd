package com.sankuai.gaigc.arrange.promptflow.bot

import com.sankuai.gaigc.arrange.common.core.bot.AIBot
import com.sankuai.gaigc.arrange.common.core.bot.dto.botknowledge.BotKnowledgeRecallResult
import com.sankuai.gaigc.arrange.common.core.bot.dto.botparam.BotRunParam
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotExecutorTypeEnum
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotProcessCodeEnum
import com.sankuai.gaigc.arrange.common.core.bot.enums.BotSourceEnum
import com.sankuai.gaigc.arrange.common.core.bot.executable.base.ExecutableKnowledgeRecall
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecutePlanContext
import com.sankuai.gaigc.arrange.common.core.bot.plan.ExecuteStep
import com.sankuai.gaigc.arrange.common.core.bot.service.BotKnowledgeRecallService
import com.sankuai.gaigc.arrange.common.core.bot.utils.SSEMessageUtils
import com.sankuai.gaigc.arrange.common.util.DateUtils
import com.sankuai.gaigc.arrange.config.MccConfig
import org.assertj.core.util.Lists
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest(SSEMessageUtils.class)
class ExecutableKnowledgeRecallSpec extends Specification {
    ExecutableKnowledgeRecall executableKnowledgeRecall
    BotKnowledgeRecallService simpleKnowledgeRecallService
    BotKnowledgeRecallService knowledgeCubeRecallService
    MccConfig mccConfig

    def setup() {
        simpleKnowledgeRecallService = Mock()
        knowledgeCubeRecallService = Mock()
        mccConfig = Mock()

        executableKnowledgeRecall = new ExecutableKnowledgeRecall(simpleKnowledgeRecallService: simpleKnowledgeRecallService,
                knowledgeCubeRecallService: knowledgeCubeRecallService,
                mccConfig: mccConfig)
    }

    @Unroll
    def "testSimpleRecall"() {
        given:
        BotKnowledgeRecallResult recallResult = new BotKnowledgeRecallResult()
        recallResult.setOriginRecallResult(Lists.newArrayList())
        simpleKnowledgeRecallService.recall(_) >> recallResult

        ExecuteStep step = new ExecuteStep()
        step.setObjective("123")
        step.setType(BotExecutorTypeEnum.KNOWLEDGE.getType())

        BotRunParam botRunParam = new BotRunParam()
        botRunParam.setUserMsg("测试哈")

        def bot = AIBot.builder()
                .botSource(BotSourceEnum.FU_XI)
                .build()
        ExecutePlanContext context = ExecutePlanContext.builder()
                .runParam(botRunParam)
                .steps(Lists.newArrayList(step))
                .bot(bot)
                .build()

        mccConfig.getKCubeKnowledgeBaseId() >> 10L
        when:
        def executeResult = executableKnowledgeRecall.execute(step, context)
        then:
        executeResult.isSuccess()
    }

    @Unroll
    def "testCubeRecall"() {
        given:
        BotKnowledgeRecallResult recallResult = new BotKnowledgeRecallResult()
        recallResult.setOriginRecallResult(Lists.newArrayList())
        knowledgeCubeRecallService.recall(_) >> recallResult

        ExecuteStep step = new ExecuteStep()
        step.setObjective("123")
        step.setType(BotExecutorTypeEnum.KNOWLEDGE.getType())

        BotRunParam botRunParam = new BotRunParam()
        botRunParam.setUserMsg("测试哈")

        def bot = AIBot.builder()
                .botSource(BotSourceEnum.K_CUBE)
                .build()
        ExecutePlanContext context = ExecutePlanContext.builder()
                .runParam(botRunParam)
                .steps(Lists.newArrayList(step))
                .bot(bot)
                .build()

        mccConfig.getKCubeKnowledgeBaseId() >> 10L
        when:
        def executeResult = executableKnowledgeRecall.execute(step, context)
        then:
        executeResult.isSuccess()
    }

    @Unroll
    def "testRecallError"() {
        given:
        knowledgeCubeRecallService.recall(_) >> { throw new RuntimeException("错误") }

        ExecuteStep step = new ExecuteStep()
        step.setObjective("123")
        step.setType(BotExecutorTypeEnum.KNOWLEDGE.getType())

        BotRunParam botRunParam = new BotRunParam()
        botRunParam.setUserMsg("测试哈")

        def bot = AIBot.builder()
                .botSource(BotSourceEnum.K_CUBE)
                .build()
        ExecutePlanContext context = ExecutePlanContext.builder()
                .runParam(botRunParam)
                .steps(Lists.newArrayList(step))
                .bot(bot)
                .build()

        mccConfig.getKCubeKnowledgeBaseId() >> 10L
        when:
        def executeResult = executableKnowledgeRecall.execute(step, context)
        then:
        def result = executeResult.getResult()
        BotKnowledgeRecallResult botKnowledgeRecallResult = (BotKnowledgeRecallResult) result.get("data")
        botKnowledgeRecallResult.getProcessCodes().contains(BotProcessCodeEnum.KNOWLEDGE_RECALL_ERROR.getValue())
    }

    @Unroll
    def "testtoLocalDateTime"() {
        when:
        def time = DateUtils.toLocalDateTime(new Date())
        then:
        Objects.nonNull(time)
    }

    @Unroll
    def "testtimestampToDatetime"() {
        when:
        def time = DateUtils.timestampToDatetime(System.currentTimeMillis())
        then:
        Objects.nonNull(time)
    }

    @Unroll
    def "testformat"() {
        when:
        def time = DateUtils.format(DateUtils.parse("2024-06-18 10:30:37", DateUtils.STANDARD_FORMAT), DateUtils.STANDARD_FORMAT)
        then:
        time == "2024-06-18 10:30:37"
    }

}
