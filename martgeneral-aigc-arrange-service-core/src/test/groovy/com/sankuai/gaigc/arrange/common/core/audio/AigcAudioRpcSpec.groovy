package com.sankuai.gaigc.arrange.common.core.audio

import com.sankuai.gaigc.arrange.api.entity.audio.AudioVO
import com.sankuai.gaigc.arrange.api.entity.audio.TTSRequest
import com.sankuai.gaigc.arrange.api.entity.audio.TTSResult
import com.sankuai.gaigc.arrange.api.entity.common.Response
import com.sankuai.gaigc.arrange.api.enums.AudioSourceEnum
import com.sankuai.gaigc.arrange.api.enums.AudioStatusEnum
import com.sankuai.gaigc.arrange.common.core.audio.entity.AudioEntity
import com.sankuai.gaigc.arrange.common.core.audio.service.AudioService
import com.sankuai.gaigc.arrange.common.core.pigeon.impl.AudioRpcServiceImpl
import spock.lang.Specification

class AigcAudioRpcSpec extends Specification {




    def "AigcAudioRpc.getOnLineAudioList测试"() {
        given:
        AudioService audioService = Mock()
        List<AudioEntity> onlineList = new ArrayList<>();
        AudioEntity entity = AudioEntity.create(40001, AudioSourceEnum.XM,"子玲（女）", "",
                "","","ziling_stream", null, AudioStatusEnum.ON)
        onlineList.add(entity)
        audioService.getOnLineAudioList() >> onlineList

        AudioRpcServiceImpl audioRpcService = new AudioRpcServiceImpl(audioService: audioService);

        when:
        Response<List<AudioVO>> response = audioRpcService.getOnLineAudioList()
        then:
        response.getData().size() == 1
    }

    def "AigcAudioRpc.text2Audio测试"() {
        given:
        AudioService audioService = Mock()
        TTSResult ttsResult = new TTSResult()
        audioService.text2Audio(_) >> ttsResult
        AudioRpcServiceImpl audioRpcService = new AudioRpcServiceImpl(audioService: audioService);

        when:
        TTSRequest ttsRequest = new TTSRequest()
        Response<TTSResult> resp = audioRpcService.text2Audio(ttsRequest)
        then:
        resp.isSuccess()
    }


}
