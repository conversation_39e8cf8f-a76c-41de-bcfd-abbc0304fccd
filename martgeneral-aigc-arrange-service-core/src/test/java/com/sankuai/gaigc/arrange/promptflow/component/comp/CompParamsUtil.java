package com.sankuai.gaigc.arrange.promptflow.component.comp;

import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentParam;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: wenhao10
 * @Description:
 * @Date: 2023-08-17 19:06
 */
public class CompParamsUtil {
    public static Map<String, ComponentParam> buildInput(Map<String, Object[]> input) {
        Map<String, ComponentParam> params = new HashMap<>();
        input.entrySet().stream().forEach(e->{
            ComponentParam componentParam = new ComponentParam();
            componentParam.setName(e.getKey());
            componentParam.setValue(e.getValue()[0]);
            componentParam.setType((ParamTypeEnum) e.getValue()[1]);
            params.put(e.getKey(), componentParam);
        });
        return params;
    }
}
