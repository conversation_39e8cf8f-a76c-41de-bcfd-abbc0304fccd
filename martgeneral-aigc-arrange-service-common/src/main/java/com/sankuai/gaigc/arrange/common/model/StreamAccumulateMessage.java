package com.sankuai.gaigc.arrange.common.model;

import com.meituan.mdp.langmodel.api.message.AssistantMessage;
import com.meituan.mdp.langmodel.api.message.FunctionCallResponse;
import com.meituan.mdp.langmodel.api.message.MdpUsage;

public class StreamAccumulateMessage extends AssistantMessage {
    private String accumulateContent;

    public StreamAccumulateMessage(FunctionCallResponse functionCallResponse) {
        super(functionCallResponse);
    }

    public StreamAccumulateMessage(FunctionCallResponse functionCallResponse, MdpUsage usage) {
        super(functionCallResponse, usage);
    }

    public StreamAccumulateMessage(String content, MdpUsage usage) {
        super(content, usage);
        this.accumulateContent = content;
    }

    public StreamAccumulateMessage(String delta, String accumulateContent) {
        super(delta);
        this.accumulateContent = accumulateContent;
    }



    public static StreamAccumulateMessage from(String delta, String accumulateContent) {
        return new StreamAccumulateMessage(delta,accumulateContent);
    }

    public static StreamAccumulateMessage from(FunctionCallResponse functionCallResponse) {
        return new StreamAccumulateMessage(functionCallResponse);
    }

    public static StreamAccumulateMessage from(String content, MdpUsage usage) {
        return new StreamAccumulateMessage(content, usage);
    }

    public static StreamAccumulateMessage from(FunctionCallResponse functionCallResponse, MdpUsage usage) {
        return new StreamAccumulateMessage(functionCallResponse, usage);
    }

    public String getAccumulateContent() {
        return accumulateContent;
    }
}