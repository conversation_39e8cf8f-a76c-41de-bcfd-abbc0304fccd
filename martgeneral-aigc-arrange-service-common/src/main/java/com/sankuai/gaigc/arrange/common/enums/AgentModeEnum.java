package com.sankuai.gaigc.arrange.common.enums;

/**
 * Agent模式
 *
 * <AUTHOR>
 * @created 2024/5/29
 */
public enum AgentModeEnum {
    ALL(-1, "ALL"),
    /** Plan-and-Execute */
    PLAN_AND_EXECUTE(1, "Planning"),
    /** T&RAG */
    TOOL_AND_RAG(2, "T&RAG"),
    /** 指定flow执行 */
    SPECIFIC_FLOW_EXECUTE(3,"Specific_Flow_Execute"),
    /** 参数匹配执行模式，识别插件出入参组装执行链路 */
    PARAM_MATCH_EXECUTE(4,"Param_Match_Execute"),
    /**
     * 多意图模式，意图识别后执行意图插件
     */
    MULTIPLE_INTENTION_EXECUTE(5, "multiple_intention_execute"),

    ;
    /** 枚举值 */
    private Integer value;
    /** 枚举描述 */
    private String desc;

    AgentModeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static AgentModeEnum getEnumByValue(Integer value) {
        for (AgentModeEnum modeEnum : AgentModeEnum.values()) {
            if (modeEnum.getValue().equals(value)) {
                return modeEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "BotExecuteModeEnum{" +
                "value=" + value +
                ", desc='" + desc + '\'' +
                '}';
    }
}