package com.sankuai.gaigc.arrange.common.enums;

public enum MessageTypeEnum {
    ACK("ack", "表示接收到了用户发起的消息"),
    PLAN("plan", "大模型生成执行计划"),
    PROCESS("process", "过程情况信息"),
    FUNCTION_CALL("function_call", "函数调用的入参"),
    FUNCTION_RESPONSE("function_response", "函数调用的结果"),
    KNOWLEDGE("knowledge", "查询知识库的结果"),
    ANSWER("answer", "大模型回复的结果"),
    FLOW_RESULT("flow_result", "流程结果"),
    BOT_RESULT("bot_result","bot执行结果"),
    FOLLOW_UP("follow_up", "用户可追问的问题提示"),
    CARD_REPLY("card_reply","卡片输出"),
    QUERY_RECOMMEND("query_recommend","猜你想问"),
    ANSWER_REFERENCE("answer_reference", "引用和归属"),
    EXPAND_INFO("expand_info","扩展信息");

    private final String value;
    private final String desc;

    MessageTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}

