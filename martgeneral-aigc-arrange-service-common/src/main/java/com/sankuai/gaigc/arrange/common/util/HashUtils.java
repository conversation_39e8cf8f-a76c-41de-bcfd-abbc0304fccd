/*
 * Copyright (c) 2022 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.common.util;

import com.google.common.hash.Hashing;
import java.nio.charset.StandardCharsets;

/**
 * 哈希工具
 *
 * <AUTHOR>
 * @created 2022/10/10
 */
public class HashUtils {

    public static Long hashString(String content) {
        return Hashing.murmur3_128().hashString(content, StandardCharsets.UTF_8).asLong();
    }

}