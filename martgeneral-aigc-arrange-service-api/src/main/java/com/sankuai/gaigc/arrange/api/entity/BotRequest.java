package com.sankuai.gaigc.arrange.api.entity;

import com.sankuai.gaigc.arrange.api.entity.bot.ChatMessageDto;
import com.sankuai.gaigc.arrange.api.entity.bot.Param;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/17 19:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BotRequest implements Serializable {
    private Long id;

    private Integer version;

    private String sessionId;

    private String userMsg;

    /**
     * 图片链接列表
     * @date 2024/12/10
     */
    private List<String> userImgUrls;

    private Map<String, String> inputParams;

    private Map<String, Param> bizParams;

    private UserVO user;

    private boolean debug = false;

    private ArenaAbRequest arenaAbRequest;

    private List<ChatMessageDto> bizMsgHistory;
}
