package com.sankuai.gaigc.arrange.api.entity.bot;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@ThriftStruct
@Getter
@Setter
public class BotCardResultDto {
    @ThriftField(value = 1)
    private List<BotCardReplyBodyDto> replyBody;

    @ThriftField(value = 2)
    private String traceMap;
}
