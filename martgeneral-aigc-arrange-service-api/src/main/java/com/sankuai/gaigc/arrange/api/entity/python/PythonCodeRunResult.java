/*
 * Copyright (c) 2025 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.api.entity.python;

import lombok.Data;

/**
 * Python代码运行结果
 *
 * <AUTHOR>
 * @created 2025/2/22
 */
@Data
public class PythonCodeRunResult {
    /** 控制台输出 */
    private String stdOut;
    /** 控制台错误 */
    private String stdError;
    /** 返回结果 */
    private String stdData;
    /** 前台数据 */
    private String frontData;
    /** 其他信息 */
    private String attachments;
}