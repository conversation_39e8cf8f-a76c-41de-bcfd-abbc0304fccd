package com.sankuai.gaigc.arrange.api.entity.bot;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@ThriftStruct
@Getter
@Setter
public class BotEnhanceResultDto {
    //引用和归属执行结果
    @ThriftField(value = 1)
    private List<AnswerReferenceKnowledgeDto> referenceKnowledge;

    //猜你想问执行结果
    //猜问有：知识库、用户配置、工作流接入三种内容来源，数据格式无法统一
    @ThriftField(value = 2)
    private List<String> queryRecommend;

    //扩展信息执行结果
    @ThriftField(value = 3)
    private List<String> expandInfos;

    //Query改写结果
    @ThriftField(value = 4)
    private String queryRewrite;
}
