package com.sankuai.aitool.runtime.script; // 本行无需修改

import com.sankuai.aitool.runtime.engine.annotation.ListItem;
import com.sankuai.aitool.runtime.engine.annotation.Parameter;
import com.sankuai.aitool.runtime.engine.model.JavaEngineCode;
import com.sankuai.aitool.runtime.engine.model.Response;
import com.sankuai.aitool.runtime.engine.rpc.RpcProcessor;
import com.sankuai.aitool.runtime.engine.rpc.impl.RpcRrocessorFactory;
import com.sankuai.aitool.runtime.engine.script.AircraftScriptExcutor;
import com.sankuai.aitool.runtime.engine.util.JacksonUtil;
import com.sankuai.aitool.runtime.engine.util.json.FreeMarkerParser;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HotelNearbyPoiRecommendScript implements AircraftScriptExcutor {

    // 必须统一使用AircraftScriptExcutor.class的Logger
    private static final Logger LOGGER = LoggerFactory.getLogger(AircraftScriptExcutor.class);
    // 日志前缀中标注AC接口ID，便于问题排查
    private static final String LOG_PREFIX = "[AC_Java_HotelNearbyPoiRecommend] ";

    @Override
    public void excutor(String paramJson, Response response) {
        try {
            LOGGER.info("{} start execute, paramJson = {}", LOG_PREFIX, paramJson);

            // 解析入参
            Double latitude = FreeMarkerParser.parseDouble(paramJson, "latitude");
            Double longitude = FreeMarkerParser.parseDouble(paramJson, "longitude");
            Integer cityId = FreeMarkerParser.parseInt(paramJson, "cityId");
            String queryContent = FreeMarkerParser.parse(paramJson, "queryContent");
            String userId = FreeMarkerParser.parse(paramJson, "userId");
            Integer orderType = FreeMarkerParser.parseInt(paramJson, "orderType");

            // 设置默认值
            if (queryContent == null || queryContent.isEmpty()) {
                queryContent = "";
            }
            if (userId == null || userId.isEmpty()) {
                userId = "-1";
            }

            // 构造HotelRequest对象
            Map<String, Object> hotelRequest = new HashMap<>();
            
            // 构造BasicRequest
            Map<String, Object> basicRequest = new HashMap<>();
            basicRequest.put("offset", 0);
            basicRequest.put("limit", 10);
            
            // 处理sort字段 - 默认为smart枚举值
            basicRequest.put("sort", "smart");
            basicRequest.put("cityId", cityId);
            
            // 如果有orderType，通过findByValue查找对应的枚举值
            if (orderType != null) {
                // 这里需要模拟SortEnum.findByValue的逻辑
                // 由于无法直接调用枚举方法，我们需要根据orderType值映射到对应的枚举名称
                String sortEnumName = getSortEnumNameByValue(orderType);
                if (sortEnumName != null) {
                    basicRequest.put("sort", sortEnumName);
                }
            }
            
            hotelRequest.put("basicRequest", basicRequest);

            // 构造AppContext
            Map<String, Object> appContext = new HashMap<>();
            Map<String, Object> position = new HashMap<>();
            position.put("lat", latitude);
            position.put("lng", longitude);
            appContext.put("position", position);
            appContext.put("userid", userId);
            
            hotelRequest.put("appContext", appContext);
            hotelRequest.put("query", queryContent);
            hotelRequest.put("isSupprtOnLineOrder", false);
            hotelRequest.put("isSales", false);
            hotelRequest.put("distance", -1);
            hotelRequest.put("sourceType", 1); // SourceType.SEARCH 对应值为1

            // 1.设置Thrift接口入参类型列表(注意顺序)
            List<String> parameterType = new ArrayList<>();
            parameterType.add("com.meituan.hotel.data.ranking.message2.HotelRequest");

            // 2.设置Thrift接口入参值列表(此处均转为String填入，注意顺序)
            List<String> parameterArgs = new ArrayList<>();
            parameterArgs.add(JacksonUtils.simpleSerialize(hotelRequest));

            // 3.获取Thrift接口实例
            // remoteAppKey: com.sankuai.dataapp.hotelsearch.mtrankdh, timeout: 10000
            RpcProcessor rpcProcessor = RpcRrocessorFactory.createThriftInstance(
                    "com.meituan.hotel.data.ranking.message2.RPCHotelRankService", 
                    "com.sankuai.dataapp.hotelsearch.mtrankdh", 
                    10000, 
                    null, 
                    null);

            // 4.调用Thrift接口
            String jsonString = rpcProcessor.invoke("search", parameterType, parameterArgs);

            LOGGER.info("{} thrift response = {}", LOG_PREFIX, jsonString);

            // 5.解析返回结果，提取POI ID列表
            List<Integer> poiIds = new ArrayList<>();
            List<Map<String, Object>> poiResults = FreeMarkerParser.convert2ListByPath(jsonString, "poiResults", Map.class);
            
            if (poiResults != null) {
                for (Map<String, Object> poiItem : poiResults) {
                    Object poiIdObj = poiItem.get("poiId");
                    if (poiIdObj != null) {
                        poiIds.add(Integer.valueOf(poiIdObj.toString()));
                    }
                }
            }

            // 6.组装AC接口返回值
            OutputParam outputParam = new OutputParam();
            outputParam.setList(poiIds);

            // 7.设置返回值
            response.setJavaEngineCode(JavaEngineCode.SUCCESS, outputParam);
            LOGGER.info("{} response = {}", LOG_PREFIX, JacksonUtil.toJsonStrWithEmptyDefault(response));
        } catch (Exception e) {
            LOGGER.error("{} invoke failed e = {}", LOG_PREFIX, e);
            response.setJavaEngineCode(JavaEngineCode.SERVER_INNER_ERROR);
        }
    }

    /**
     * 模拟SortEnum.findByValue的逻辑
     * 根据orderType值映射到对应的枚举名称
     */
    private String getSortEnumNameByValue(Integer orderType) {
        // 根据完整的SortEnum定义进行映射
        switch (orderType) {
            case 0:
                return "distance_asc";
            case 1:
                return "lowestprice_asc";
            case 2:
                return "lowestprice_desc";
            case 3:
                return "avgscore_desc";
            case 4:
                return "smart";
            case 5:
                return "natural";
            case 6:
                return "solds_desc";
            case 7:
                return "starttime_desc";
            case 8:
                return "fbcount_desc";
            case 9:
                return "latesthousing_desc";
            case 10:
                return "bujia_asc";
            case 11:
                return "reviewcount_desc";
            default:
                return null; // 如果找不到对应的枚举值，返回null
        }
    }

    @Data
    public static class InputParam {
        @Parameter(name="纬度")
        private Double latitude;
        
        @Parameter(name="经度")
        private Double longitude;
        
        @Parameter(name="城市ID")
        private Integer cityId;
        
        @Parameter(name="查询内容", defaultValue="")
        private String queryContent;
        
        @Parameter(name="用户ID", defaultValue="-1")
        private String userId;
        
        @Parameter(name="排序类型")
        private Integer orderType;
    }

    @Data
    public static class OutputParam {
        @ListItem
        private List<Integer> list;
    }
}  