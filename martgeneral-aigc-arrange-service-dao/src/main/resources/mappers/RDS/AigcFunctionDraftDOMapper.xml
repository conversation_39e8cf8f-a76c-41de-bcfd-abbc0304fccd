<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.gaigc.arrange.dao.dal.mapper.AigcFunctionDraftDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cn_name" jdbcType="VARCHAR" property="cnName" />
    <result column="func_type" jdbcType="INTEGER" property="funcType" />
    <result column="usage_scenario" jdbcType="INTEGER" property="usageScenario" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="test_status" jdbcType="INTEGER" property="testStatus" />
    <result column="online_status" jdbcType="INTEGER" property="onlineStatus" />
    <result column="function_id" jdbcType="BIGINT" property="functionId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="call_info" jdbcType="LONGVARCHAR" property="callInfo" />
    <result column="param_info" jdbcType="LONGVARCHAR" property="paramInfo" />
    <result column="follow_up_info" jdbcType="LONGVARCHAR" property="followUpInfo" />
    <result column="ext" jdbcType="LONGVARCHAR" property="ext" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, cn_name, func_type, usage_scenario, creator, modifier, create_time, update_time, 
    test_status, online_status, function_id
  </sql>
  <sql id="Blob_Column_List">
    description, call_info, param_info, follow_up_info, ext
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.gaigc.arrange.dao.dal.example.aigcFunctionDraftDOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from aigc_function_draft
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.gaigc.arrange.dao.dal.example.aigcFunctionDraftDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aigc_function_draft
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from aigc_function_draft
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aigc_function_draft
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.gaigc.arrange.dao.dal.example.aigcFunctionDraftDOExample">
    delete from aigc_function_draft
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aigc_function_draft (name, cn_name, func_type,
      usage_scenario, creator, modifier, 
      create_time, update_time, test_status, 
      online_status, function_id, description, 
      call_info, param_info, follow_up_info, 
      ext)
    values (#{name,jdbcType=VARCHAR}, #{cnName,jdbcType=VARCHAR}, #{funcType,jdbcType=INTEGER}, 
      #{usageScenario,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{testStatus,jdbcType=INTEGER}, 
      #{onlineStatus,jdbcType=INTEGER}, #{functionId,jdbcType=BIGINT}, #{description,jdbcType=LONGVARCHAR}, 
      #{callInfo,jdbcType=LONGVARCHAR}, #{paramInfo,jdbcType=LONGVARCHAR}, #{followUpInfo,jdbcType=LONGVARCHAR}, 
      #{ext,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aigc_function_draft
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="cnName != null">
        cn_name,
      </if>
      <if test="funcType != null">
        func_type,
      </if>
      <if test="usageScenario != null">
        usage_scenario,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="testStatus != null">
        test_status,
      </if>
      <if test="onlineStatus != null">
        online_status,
      </if>
      <if test="functionId != null">
        function_id,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="callInfo != null">
        call_info,
      </if>
      <if test="paramInfo != null">
        param_info,
      </if>
      <if test="followUpInfo != null">
        follow_up_info,
      </if>
      <if test="ext != null">
        ext,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="cnName != null">
        #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="funcType != null">
        #{funcType,jdbcType=INTEGER},
      </if>
      <if test="usageScenario != null">
        #{usageScenario,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="testStatus != null">
        #{testStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineStatus != null">
        #{onlineStatus,jdbcType=INTEGER},
      </if>
      <if test="functionId != null">
        #{functionId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="callInfo != null">
        #{callInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="paramInfo != null">
        #{paramInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="followUpInfo != null">
        #{followUpInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.gaigc.arrange.dao.dal.example.aigcFunctionDraftDOExample" resultType="java.lang.Long">
    select count(*) from aigc_function_draft
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update aigc_function_draft
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.cnName != null">
        cn_name = #{record.cnName,jdbcType=VARCHAR},
      </if>
      <if test="record.funcType != null">
        func_type = #{record.funcType,jdbcType=INTEGER},
      </if>
      <if test="record.usageScenario != null">
        usage_scenario = #{record.usageScenario,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.testStatus != null">
        test_status = #{record.testStatus,jdbcType=INTEGER},
      </if>
      <if test="record.onlineStatus != null">
        online_status = #{record.onlineStatus,jdbcType=INTEGER},
      </if>
      <if test="record.functionId != null">
        function_id = #{record.functionId,jdbcType=BIGINT},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.callInfo != null">
        call_info = #{record.callInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.paramInfo != null">
        param_info = #{record.paramInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.followUpInfo != null">
        follow_up_info = #{record.followUpInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update aigc_function_draft
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      cn_name = #{record.cnName,jdbcType=VARCHAR},
      func_type = #{record.funcType,jdbcType=INTEGER},
      usage_scenario = #{record.usageScenario,jdbcType=INTEGER},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      test_status = #{record.testStatus,jdbcType=INTEGER},
      online_status = #{record.onlineStatus,jdbcType=INTEGER},
      function_id = #{record.functionId,jdbcType=BIGINT},
      description = #{record.description,jdbcType=LONGVARCHAR},
      call_info = #{record.callInfo,jdbcType=LONGVARCHAR},
      param_info = #{record.paramInfo,jdbcType=LONGVARCHAR},
      follow_up_info = #{record.followUpInfo,jdbcType=LONGVARCHAR},
      ext = #{record.ext,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update aigc_function_draft
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      cn_name = #{record.cnName,jdbcType=VARCHAR},
      func_type = #{record.funcType,jdbcType=INTEGER},
      usage_scenario = #{record.usageScenario,jdbcType=INTEGER},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      test_status = #{record.testStatus,jdbcType=INTEGER},
      online_status = #{record.onlineStatus,jdbcType=INTEGER},
      function_id = #{record.functionId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO">
    update aigc_function_draft
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="cnName != null">
        cn_name = #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="funcType != null">
        func_type = #{funcType,jdbcType=INTEGER},
      </if>
      <if test="usageScenario != null">
        usage_scenario = #{usageScenario,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="testStatus != null">
        test_status = #{testStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineStatus != null">
        online_status = #{onlineStatus,jdbcType=INTEGER},
      </if>
      <if test="functionId != null">
        function_id = #{functionId,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
      <if test="callInfo != null">
        call_info = #{callInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="paramInfo != null">
        param_info = #{paramInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="followUpInfo != null">
        follow_up_info = #{followUpInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO">
    update aigc_function_draft
    set name = #{name,jdbcType=VARCHAR},
      cn_name = #{cnName,jdbcType=VARCHAR},
      func_type = #{funcType,jdbcType=INTEGER},
      usage_scenario = #{usageScenario,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      test_status = #{testStatus,jdbcType=INTEGER},
      online_status = #{onlineStatus,jdbcType=INTEGER},
      function_id = #{functionId,jdbcType=BIGINT},
      description = #{description,jdbcType=LONGVARCHAR},
      call_info = #{callInfo,jdbcType=LONGVARCHAR},
      param_info = #{paramInfo,jdbcType=LONGVARCHAR},
      follow_up_info = #{followUpInfo,jdbcType=LONGVARCHAR},
      ext = #{ext,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.aigcFunctionDraftDO">
    update aigc_function_draft
    set name = #{name,jdbcType=VARCHAR},
      cn_name = #{cnName,jdbcType=VARCHAR},
      func_type = #{funcType,jdbcType=INTEGER},
      usage_scenario = #{usageScenario,jdbcType=INTEGER},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      test_status = #{testStatus,jdbcType=INTEGER},
      online_status = #{onlineStatus,jdbcType=INTEGER},
      function_id = #{functionId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into aigc_function_draft
    (name, cn_name, func_type, usage_scenario, creator, modifier, create_time, update_time, 
      test_status, online_status, function_id, description, call_info, param_info, follow_up_info, 
      ext)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.cnName,jdbcType=VARCHAR}, #{item.funcType,jdbcType=INTEGER}, 
        #{item.usageScenario,jdbcType=INTEGER}, #{item.creator,jdbcType=VARCHAR}, #{item.modifier,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.testStatus,jdbcType=INTEGER}, 
        #{item.onlineStatus,jdbcType=INTEGER}, #{item.functionId,jdbcType=BIGINT}, #{item.description,jdbcType=LONGVARCHAR}, 
        #{item.callInfo,jdbcType=LONGVARCHAR}, #{item.paramInfo,jdbcType=LONGVARCHAR}, 
        #{item.followUpInfo,jdbcType=LONGVARCHAR}, #{item.ext,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>