<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.gaigc.arrange.dao.dal.mapper.AIBotSnapshotDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bot_id" jdbcType="BIGINT" property="botId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="plugins" jdbcType="VARCHAR" property="plugins" />
    <result column="flows" jdbcType="VARCHAR" property="flows" />
    <result column="knowledge_bases" jdbcType="VARCHAR" property="knowledgeBases" />
    <result column="context_round" jdbcType="INTEGER" property="contextRound" />
    <result column="fallback_reply_switch" jdbcType="INTEGER" property="fallbackReplySwitch" />
    <result column="mode" jdbcType="INTEGER" property="mode" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="version_desc" jdbcType="VARCHAR" property="versionDesc" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="long_memory_switch" jdbcType="INTEGER" property="longMemorySwitch" />
    <result column="custom_intent_config" jdbcType="VARCHAR" property="customIntentConfig" />
    <result column="multi_intent_mode" jdbcType="INTEGER" property="multiIntentMode" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO">
    <result column="character_prompt" jdbcType="LONGVARCHAR" property="characterPrompt" />
    <result column="minor_prompt" jdbcType="LONGVARCHAR" property="minorPrompt" />
    <result column="model_config" jdbcType="LONGVARCHAR" property="modelConfig" />
    <result column="prologue" jdbcType="LONGVARCHAR" property="prologue" />
    <result column="pre_questions" jdbcType="LONGVARCHAR" property="preQuestions" />
    <result column="fallback_reply" jdbcType="LONGVARCHAR" property="fallbackReply" />
    <result column="variable" jdbcType="LONGVARCHAR" property="variable" />
    <result column="enhance" jdbcType="LONGVARCHAR" property="enhance" />
    <result column="extension" jdbcType="LONGVARCHAR" property="extension" />
    <result column="regular_reply" jdbcType="LONGVARCHAR" property="regularReply" />
    <result column="ai_card_config" jdbcType="LONGVARCHAR" property="aiCardConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bot_id, creator, plugins, flows, knowledge_bases, context_round, fallback_reply_switch, 
    mode, version, version_desc, status, create_time, update_time, long_memory_switch, 
    custom_intent_config, multi_intent_mode, publish_time
  </sql>
  <sql id="Blob_Column_List">
    character_prompt, minor_prompt, model_config, prologue, pre_questions, fallback_reply, 
    variable, enhance, extension, regular_reply, ai_card_config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.gaigc.arrange.dao.dal.example.AIBotSnapshotDOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_bot_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.gaigc.arrange.dao.dal.example.AIBotSnapshotDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ai_bot_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="rows != null">
      <if test="offset != null">
        limit ${offset}, ${rows}
      </if>
      <if test="offset == null">
        limit ${rows}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ai_bot_snapshot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ai_bot_snapshot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.gaigc.arrange.dao.dal.example.AIBotSnapshotDOExample">
    delete from ai_bot_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ai_bot_snapshot (bot_id, creator, plugins, 
      flows, knowledge_bases, context_round, 
      fallback_reply_switch, mode, version, 
      version_desc, status, create_time, 
      update_time, long_memory_switch, custom_intent_config, 
      multi_intent_mode, publish_time, character_prompt, 
      minor_prompt, model_config, prologue, 
      pre_questions, fallback_reply, variable, 
      enhance, extension, regular_reply, 
      ai_card_config)
    values (#{botId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR}, #{plugins,jdbcType=VARCHAR}, 
      #{flows,jdbcType=VARCHAR}, #{knowledgeBases,jdbcType=VARCHAR}, #{contextRound,jdbcType=INTEGER}, 
      #{fallbackReplySwitch,jdbcType=INTEGER}, #{mode,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}, 
      #{versionDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{longMemorySwitch,jdbcType=INTEGER}, #{customIntentConfig,jdbcType=VARCHAR}, 
      #{multiIntentMode,jdbcType=INTEGER}, #{publishTime,jdbcType=TIMESTAMP}, #{characterPrompt,jdbcType=LONGVARCHAR}, 
      #{minorPrompt,jdbcType=LONGVARCHAR}, #{modelConfig,jdbcType=LONGVARCHAR}, #{prologue,jdbcType=LONGVARCHAR}, 
      #{preQuestions,jdbcType=LONGVARCHAR}, #{fallbackReply,jdbcType=LONGVARCHAR}, #{variable,jdbcType=LONGVARCHAR}, 
      #{enhance,jdbcType=LONGVARCHAR}, #{extension,jdbcType=LONGVARCHAR}, #{regularReply,jdbcType=LONGVARCHAR}, 
      #{aiCardConfig,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ai_bot_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="botId != null">
        bot_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="plugins != null">
        plugins,
      </if>
      <if test="flows != null">
        flows,
      </if>
      <if test="knowledgeBases != null">
        knowledge_bases,
      </if>
      <if test="contextRound != null">
        context_round,
      </if>
      <if test="fallbackReplySwitch != null">
        fallback_reply_switch,
      </if>
      <if test="mode != null">
        mode,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="versionDesc != null">
        version_desc,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="longMemorySwitch != null">
        long_memory_switch,
      </if>
      <if test="customIntentConfig != null">
        custom_intent_config,
      </if>
      <if test="multiIntentMode != null">
        multi_intent_mode,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="characterPrompt != null">
        character_prompt,
      </if>
      <if test="minorPrompt != null">
        minor_prompt,
      </if>
      <if test="modelConfig != null">
        model_config,
      </if>
      <if test="prologue != null">
        prologue,
      </if>
      <if test="preQuestions != null">
        pre_questions,
      </if>
      <if test="fallbackReply != null">
        fallback_reply,
      </if>
      <if test="variable != null">
        variable,
      </if>
      <if test="enhance != null">
        enhance,
      </if>
      <if test="extension != null">
        extension,
      </if>
      <if test="regularReply != null">
        regular_reply,
      </if>
      <if test="aiCardConfig != null">
        ai_card_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="botId != null">
        #{botId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="plugins != null">
        #{plugins,jdbcType=VARCHAR},
      </if>
      <if test="flows != null">
        #{flows,jdbcType=VARCHAR},
      </if>
      <if test="knowledgeBases != null">
        #{knowledgeBases,jdbcType=VARCHAR},
      </if>
      <if test="contextRound != null">
        #{contextRound,jdbcType=INTEGER},
      </if>
      <if test="fallbackReplySwitch != null">
        #{fallbackReplySwitch,jdbcType=INTEGER},
      </if>
      <if test="mode != null">
        #{mode,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="versionDesc != null">
        #{versionDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="longMemorySwitch != null">
        #{longMemorySwitch,jdbcType=INTEGER},
      </if>
      <if test="customIntentConfig != null">
        #{customIntentConfig,jdbcType=VARCHAR},
      </if>
      <if test="multiIntentMode != null">
        #{multiIntentMode,jdbcType=INTEGER},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="characterPrompt != null">
        #{characterPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="minorPrompt != null">
        #{minorPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="modelConfig != null">
        #{modelConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="prologue != null">
        #{prologue,jdbcType=LONGVARCHAR},
      </if>
      <if test="preQuestions != null">
        #{preQuestions,jdbcType=LONGVARCHAR},
      </if>
      <if test="fallbackReply != null">
        #{fallbackReply,jdbcType=LONGVARCHAR},
      </if>
      <if test="variable != null">
        #{variable,jdbcType=LONGVARCHAR},
      </if>
      <if test="enhance != null">
        #{enhance,jdbcType=LONGVARCHAR},
      </if>
      <if test="extension != null">
        #{extension,jdbcType=LONGVARCHAR},
      </if>
      <if test="regularReply != null">
        #{regularReply,jdbcType=LONGVARCHAR},
      </if>
      <if test="aiCardConfig != null">
        #{aiCardConfig,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.gaigc.arrange.dao.dal.example.AIBotSnapshotDOExample" resultType="java.lang.Long">
    select count(*) from ai_bot_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ai_bot_snapshot
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.botId != null">
        bot_id = #{record.botId,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.plugins != null">
        plugins = #{record.plugins,jdbcType=VARCHAR},
      </if>
      <if test="record.flows != null">
        flows = #{record.flows,jdbcType=VARCHAR},
      </if>
      <if test="record.knowledgeBases != null">
        knowledge_bases = #{record.knowledgeBases,jdbcType=VARCHAR},
      </if>
      <if test="record.contextRound != null">
        context_round = #{record.contextRound,jdbcType=INTEGER},
      </if>
      <if test="record.fallbackReplySwitch != null">
        fallback_reply_switch = #{record.fallbackReplySwitch,jdbcType=INTEGER},
      </if>
      <if test="record.mode != null">
        mode = #{record.mode,jdbcType=INTEGER},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.versionDesc != null">
        version_desc = #{record.versionDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.longMemorySwitch != null">
        long_memory_switch = #{record.longMemorySwitch,jdbcType=INTEGER},
      </if>
      <if test="record.customIntentConfig != null">
        custom_intent_config = #{record.customIntentConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.multiIntentMode != null">
        multi_intent_mode = #{record.multiIntentMode,jdbcType=INTEGER},
      </if>
      <if test="record.publishTime != null">
        publish_time = #{record.publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.characterPrompt != null">
        character_prompt = #{record.characterPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.minorPrompt != null">
        minor_prompt = #{record.minorPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.modelConfig != null">
        model_config = #{record.modelConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.prologue != null">
        prologue = #{record.prologue,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.preQuestions != null">
        pre_questions = #{record.preQuestions,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.fallbackReply != null">
        fallback_reply = #{record.fallbackReply,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.variable != null">
        variable = #{record.variable,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.enhance != null">
        enhance = #{record.enhance,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.extension != null">
        extension = #{record.extension,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.regularReply != null">
        regular_reply = #{record.regularReply,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.aiCardConfig != null">
        ai_card_config = #{record.aiCardConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update ai_bot_snapshot
    set id = #{record.id,jdbcType=BIGINT},
      bot_id = #{record.botId,jdbcType=BIGINT},
      creator = #{record.creator,jdbcType=VARCHAR},
      plugins = #{record.plugins,jdbcType=VARCHAR},
      flows = #{record.flows,jdbcType=VARCHAR},
      knowledge_bases = #{record.knowledgeBases,jdbcType=VARCHAR},
      context_round = #{record.contextRound,jdbcType=INTEGER},
      fallback_reply_switch = #{record.fallbackReplySwitch,jdbcType=INTEGER},
      mode = #{record.mode,jdbcType=INTEGER},
      version = #{record.version,jdbcType=INTEGER},
      version_desc = #{record.versionDesc,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      long_memory_switch = #{record.longMemorySwitch,jdbcType=INTEGER},
      custom_intent_config = #{record.customIntentConfig,jdbcType=VARCHAR},
      multi_intent_mode = #{record.multiIntentMode,jdbcType=INTEGER},
      publish_time = #{record.publishTime,jdbcType=TIMESTAMP},
      character_prompt = #{record.characterPrompt,jdbcType=LONGVARCHAR},
      minor_prompt = #{record.minorPrompt,jdbcType=LONGVARCHAR},
      model_config = #{record.modelConfig,jdbcType=LONGVARCHAR},
      prologue = #{record.prologue,jdbcType=LONGVARCHAR},
      pre_questions = #{record.preQuestions,jdbcType=LONGVARCHAR},
      fallback_reply = #{record.fallbackReply,jdbcType=LONGVARCHAR},
      variable = #{record.variable,jdbcType=LONGVARCHAR},
      enhance = #{record.enhance,jdbcType=LONGVARCHAR},
      extension = #{record.extension,jdbcType=LONGVARCHAR},
      regular_reply = #{record.regularReply,jdbcType=LONGVARCHAR},
      ai_card_config = #{record.aiCardConfig,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ai_bot_snapshot
    set id = #{record.id,jdbcType=BIGINT},
      bot_id = #{record.botId,jdbcType=BIGINT},
      creator = #{record.creator,jdbcType=VARCHAR},
      plugins = #{record.plugins,jdbcType=VARCHAR},
      flows = #{record.flows,jdbcType=VARCHAR},
      knowledge_bases = #{record.knowledgeBases,jdbcType=VARCHAR},
      context_round = #{record.contextRound,jdbcType=INTEGER},
      fallback_reply_switch = #{record.fallbackReplySwitch,jdbcType=INTEGER},
      mode = #{record.mode,jdbcType=INTEGER},
      version = #{record.version,jdbcType=INTEGER},
      version_desc = #{record.versionDesc,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      long_memory_switch = #{record.longMemorySwitch,jdbcType=INTEGER},
      custom_intent_config = #{record.customIntentConfig,jdbcType=VARCHAR},
      multi_intent_mode = #{record.multiIntentMode,jdbcType=INTEGER},
      publish_time = #{record.publishTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO">
    update ai_bot_snapshot
    <set>
      <if test="botId != null">
        bot_id = #{botId,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="plugins != null">
        plugins = #{plugins,jdbcType=VARCHAR},
      </if>
      <if test="flows != null">
        flows = #{flows,jdbcType=VARCHAR},
      </if>
      <if test="knowledgeBases != null">
        knowledge_bases = #{knowledgeBases,jdbcType=VARCHAR},
      </if>
      <if test="contextRound != null">
        context_round = #{contextRound,jdbcType=INTEGER},
      </if>
      <if test="fallbackReplySwitch != null">
        fallback_reply_switch = #{fallbackReplySwitch,jdbcType=INTEGER},
      </if>
      <if test="mode != null">
        mode = #{mode,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="versionDesc != null">
        version_desc = #{versionDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="longMemorySwitch != null">
        long_memory_switch = #{longMemorySwitch,jdbcType=INTEGER},
      </if>
      <if test="customIntentConfig != null">
        custom_intent_config = #{customIntentConfig,jdbcType=VARCHAR},
      </if>
      <if test="multiIntentMode != null">
        multi_intent_mode = #{multiIntentMode,jdbcType=INTEGER},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="characterPrompt != null">
        character_prompt = #{characterPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="minorPrompt != null">
        minor_prompt = #{minorPrompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="modelConfig != null">
        model_config = #{modelConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="prologue != null">
        prologue = #{prologue,jdbcType=LONGVARCHAR},
      </if>
      <if test="preQuestions != null">
        pre_questions = #{preQuestions,jdbcType=LONGVARCHAR},
      </if>
      <if test="fallbackReply != null">
        fallback_reply = #{fallbackReply,jdbcType=LONGVARCHAR},
      </if>
      <if test="variable != null">
        variable = #{variable,jdbcType=LONGVARCHAR},
      </if>
      <if test="enhance != null">
        enhance = #{enhance,jdbcType=LONGVARCHAR},
      </if>
      <if test="extension != null">
        extension = #{extension,jdbcType=LONGVARCHAR},
      </if>
      <if test="regularReply != null">
        regular_reply = #{regularReply,jdbcType=LONGVARCHAR},
      </if>
      <if test="aiCardConfig != null">
        ai_card_config = #{aiCardConfig,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO">
    update ai_bot_snapshot
    set bot_id = #{botId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=VARCHAR},
      plugins = #{plugins,jdbcType=VARCHAR},
      flows = #{flows,jdbcType=VARCHAR},
      knowledge_bases = #{knowledgeBases,jdbcType=VARCHAR},
      context_round = #{contextRound,jdbcType=INTEGER},
      fallback_reply_switch = #{fallbackReplySwitch,jdbcType=INTEGER},
      mode = #{mode,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      version_desc = #{versionDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      long_memory_switch = #{longMemorySwitch,jdbcType=INTEGER},
      custom_intent_config = #{customIntentConfig,jdbcType=VARCHAR},
      multi_intent_mode = #{multiIntentMode,jdbcType=INTEGER},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      character_prompt = #{characterPrompt,jdbcType=LONGVARCHAR},
      minor_prompt = #{minorPrompt,jdbcType=LONGVARCHAR},
      model_config = #{modelConfig,jdbcType=LONGVARCHAR},
      prologue = #{prologue,jdbcType=LONGVARCHAR},
      pre_questions = #{preQuestions,jdbcType=LONGVARCHAR},
      fallback_reply = #{fallbackReply,jdbcType=LONGVARCHAR},
      variable = #{variable,jdbcType=LONGVARCHAR},
      enhance = #{enhance,jdbcType=LONGVARCHAR},
      extension = #{extension,jdbcType=LONGVARCHAR},
      regular_reply = #{regularReply,jdbcType=LONGVARCHAR},
      ai_card_config = #{aiCardConfig,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.gaigc.arrange.dao.dal.entity.AIBotSnapshotDO">
    update ai_bot_snapshot
    set bot_id = #{botId,jdbcType=BIGINT},
      creator = #{creator,jdbcType=VARCHAR},
      plugins = #{plugins,jdbcType=VARCHAR},
      flows = #{flows,jdbcType=VARCHAR},
      knowledge_bases = #{knowledgeBases,jdbcType=VARCHAR},
      context_round = #{contextRound,jdbcType=INTEGER},
      fallback_reply_switch = #{fallbackReplySwitch,jdbcType=INTEGER},
      mode = #{mode,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      version_desc = #{versionDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      long_memory_switch = #{longMemorySwitch,jdbcType=INTEGER},
      custom_intent_config = #{customIntentConfig,jdbcType=VARCHAR},
      multi_intent_mode = #{multiIntentMode,jdbcType=INTEGER},
      publish_time = #{publishTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    insert into ai_bot_snapshot
    (bot_id, creator, plugins, flows, knowledge_bases, context_round, fallback_reply_switch, 
      mode, version, version_desc, status, create_time, update_time, long_memory_switch, 
      custom_intent_config, multi_intent_mode, publish_time, character_prompt, minor_prompt, 
      model_config, prologue, pre_questions, fallback_reply, variable, enhance, extension, 
      regular_reply, ai_card_config)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.botId,jdbcType=BIGINT}, #{item.creator,jdbcType=VARCHAR}, #{item.plugins,jdbcType=VARCHAR}, 
        #{item.flows,jdbcType=VARCHAR}, #{item.knowledgeBases,jdbcType=VARCHAR}, #{item.contextRound,jdbcType=INTEGER}, 
        #{item.fallbackReplySwitch,jdbcType=INTEGER}, #{item.mode,jdbcType=INTEGER}, #{item.version,jdbcType=INTEGER}, 
        #{item.versionDesc,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.longMemorySwitch,jdbcType=INTEGER}, 
        #{item.customIntentConfig,jdbcType=VARCHAR}, #{item.multiIntentMode,jdbcType=INTEGER}, 
        #{item.publishTime,jdbcType=TIMESTAMP}, #{item.characterPrompt,jdbcType=LONGVARCHAR}, 
        #{item.minorPrompt,jdbcType=LONGVARCHAR}, #{item.modelConfig,jdbcType=LONGVARCHAR}, 
        #{item.prologue,jdbcType=LONGVARCHAR}, #{item.preQuestions,jdbcType=LONGVARCHAR}, 
        #{item.fallbackReply,jdbcType=LONGVARCHAR}, #{item.variable,jdbcType=LONGVARCHAR}, 
        #{item.enhance,jdbcType=LONGVARCHAR}, #{item.extension,jdbcType=LONGVARCHAR}, #{item.regularReply,jdbcType=LONGVARCHAR}, 
        #{item.aiCardConfig,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
</mapper>