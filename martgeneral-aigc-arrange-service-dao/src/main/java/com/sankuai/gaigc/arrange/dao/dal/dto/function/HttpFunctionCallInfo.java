/*
 * Copyright (c) 2023 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.dao.dal.dto.function;

import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * HttpFunctionCallInfo
 *
 * <AUTHOR>
 * @created 2023/11/9
 */
@Getter
@Setter
@ToString
public class HttpFunctionCallInfo extends FunctionCallInfo {
    /** 接口路径 */
    private String httpUrl;
    /** 请求方式 */
    private String httpMethod;
}
