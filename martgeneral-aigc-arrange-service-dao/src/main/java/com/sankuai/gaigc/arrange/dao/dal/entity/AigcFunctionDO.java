/*
 * Copyright (c) 2023 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.dao.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 函数信息
 *
 * <AUTHOR>
 * @created 2023/12/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AigcFunctionDO {
    /** 自增ID */
    private Long id;
    /** 函数名称 */
    private String name;
    /** 中文名称 */
    private String cnName;
    /** 函数描述 */
    private String description;
    /** 函数类型 */
    private Integer funcType;
    /** 调用信息 */
    private String callInfo;
    /** 参数信息 */
    private String paramInfo;
    /** 创建人 */
    private String creator;
    /** 修改人 */
    private String modifier;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    /** 追问信息 */
    private String followUpInfo;
    /** ext */
    private String ext;
}
