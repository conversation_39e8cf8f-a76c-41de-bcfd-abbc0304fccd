/*
 * Copyright (c) 2023 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.dao.dal.dto.function;

import java.util.List;
import lombok.Data;

/**
 * ParamDefineModel
 *
 * <AUTHOR>
 * @created 2023/11/9
 */
@Data
public class ParamDefineModel {
    /** 参数标识 */
    private String identity;
    /** 名称 */
    private String name;
    /** 描述 */
    private String desc;
    /** 是否必填 */
    private boolean required;
    /** 参数类型,FunctionParamTypeEnum */
    private String dataType;
    /** 默认值 */
    private String defaultValue;
    /** 枚举值 */
    private List<String> enumValues;
    /** 参数类 */
    private String paramClass;

    /** 对象属性信息 */
    private List<ParamDefineModel> objAttributes;
    /** 数组元素信息 */
    private ParamDefineModel arrayElement;
}
