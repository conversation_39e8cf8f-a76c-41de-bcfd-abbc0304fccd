package com.sankuai.gaigc.arrange.dao.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: aigc_function_draft
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class aigcFunctionDraftDO {
    /**
     *   字段: id
     *   说明: 函数ID
     */
    private Long id;

    /**
     *   字段: name
     *   说明: 函数名称
     */
    private String name;

    /**
     *   字段: cn_name
     *   说明: 中文名称
     */
    private String cnName;

    /**
     *   字段: func_type
     *   说明: 函数类型，1：http，2：mtthrift，3：pigeon，4:：local method
     */
    private Integer funcType;

    /**
     *   字段: usage_scenario
     *   说明: 使用场景 1工作流 2聊天助手 3全部
     */
    private Integer usageScenario;

    /**
     *   字段: creator
     *   说明: 创建人
     */
    private String creator;

    /**
     *   字段: modifier
     *   说明: 修改人
     */
    private String modifier;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 修改时间
     */
    private Date updateTime;

    /**
     *   字段: test_status
     *   说明: 测评状态
     */
    private Integer testStatus;

    /**
     *   字段: online_status
     *   说明: 上线状态
     */
    private Integer onlineStatus;

    /**
     *   字段: function_id
     *   说明: function_id
     */
    private Long functionId;

    /**
     *   字段: description
     *   说明: 函数描述
     */
    private String description;

    /**
     *   字段: call_info
     *   说明: 调用信息，JSON
     */
    private String callInfo;

    /**
     *   字段: param_info
     *   说明: 函数参数信息，JSON
     */
    private String paramInfo;

    /**
     *   字段: follow_up_info
     *   说明: 追问信息，JSONArray
     */
    private String followUpInfo;

    /**
     *   字段: ext
     *   说明: 拓展
     */
    private String ext;
}