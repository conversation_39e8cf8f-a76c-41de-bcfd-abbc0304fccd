/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.dao.dal.dto.knowledge;

import lombok.Data;

/**
 * 知识库字段
 *
 * <AUTHOR>
 * @created 2024/4/7
 */
@Data
public class KnowledgeBaseField {
    /** 字段名称 */
    private String fieldName;
    /**
     * 字段类型
     *
     * @see com.sankuai.gaigc.arrange.dao.dal.enums.KnowledgeBaseFieldTypeEnum
     */
    private Integer fieldType;
    /** 默认值 */
    private String defaultValue;
    /** 字段描述 */
    private String fieldDesc;
    /** 是否为向量化字段 */
    private boolean asEmbedding;
    /** 是否全文检索索引 */
    private boolean asFullTextIndex;
}
