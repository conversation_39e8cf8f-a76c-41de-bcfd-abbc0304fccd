package com.sankuai.gaigc.arrange.dao.dal.dto.function;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * date 2024/4/15
 */
@Getter
@Setter
@ToString
public class LocalMethodFunctionCallInfo extends FunctionCallInfo {
    /**
     * 类名
     * */
    private String serviceName;
    /**
     * 方法名
     * */
    private String methodName;
    /**
     * true：从Spring容器中获取实例；false：通过反射获取实例
     * */
    private Boolean isSpringBean;
}
