package com.sankuai.gaigc.arrange.dao.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BotAbtestInfoDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    public BotAbtestInfoDOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
        rows = null;
        offset = null;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return this.offset;
    }

    public void setRows(Integer rows) {
        this.rows = rows;
    }

    public Integer getRows() {
        return this.rows;
    }

    public BotAbtestInfoDOExample limit(Integer rows) {
        this.rows = rows;
        return this;
    }

    public BotAbtestInfoDOExample limit(Integer offset, Integer rows) {
        this.offset = offset;
        this.rows = rows;
        return this;
    }

    public BotAbtestInfoDOExample page(Integer page, Integer pageSize) {
        this.offset = page * pageSize;
        this.rows = pageSize;
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBotIdIsNull() {
            addCriterion("bot_id is null");
            return (Criteria) this;
        }

        public Criteria andBotIdIsNotNull() {
            addCriterion("bot_id is not null");
            return (Criteria) this;
        }

        public Criteria andBotIdEqualTo(Long value) {
            addCriterion("bot_id =", value, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdNotEqualTo(Long value) {
            addCriterion("bot_id <>", value, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdGreaterThan(Long value) {
            addCriterion("bot_id >", value, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdGreaterThanOrEqualTo(Long value) {
            addCriterion("bot_id >=", value, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdLessThan(Long value) {
            addCriterion("bot_id <", value, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdLessThanOrEqualTo(Long value) {
            addCriterion("bot_id <=", value, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdIn(List<Long> values) {
            addCriterion("bot_id in", values, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdNotIn(List<Long> values) {
            addCriterion("bot_id not in", values, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdBetween(Long value1, Long value2) {
            addCriterion("bot_id between", value1, value2, "botId");
            return (Criteria) this;
        }

        public Criteria andBotIdNotBetween(Long value1, Long value2) {
            addCriterion("bot_id not between", value1, value2, "botId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyIsNull() {
            addCriterion("arena_exp_key is null");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyIsNotNull() {
            addCriterion("arena_exp_key is not null");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyEqualTo(String value) {
            addCriterion("arena_exp_key =", value, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyNotEqualTo(String value) {
            addCriterion("arena_exp_key <>", value, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyGreaterThan(String value) {
            addCriterion("arena_exp_key >", value, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyGreaterThanOrEqualTo(String value) {
            addCriterion("arena_exp_key >=", value, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyLessThan(String value) {
            addCriterion("arena_exp_key <", value, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyLessThanOrEqualTo(String value) {
            addCriterion("arena_exp_key <=", value, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyLike(String value) {
            addCriterion("arena_exp_key like", value, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyNotLike(String value) {
            addCriterion("arena_exp_key not like", value, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyIn(List<String> values) {
            addCriterion("arena_exp_key in", values, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyNotIn(List<String> values) {
            addCriterion("arena_exp_key not in", values, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyBetween(String value1, String value2) {
            addCriterion("arena_exp_key between", value1, value2, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpKeyNotBetween(String value1, String value2) {
            addCriterion("arena_exp_key not between", value1, value2, "arenaExpKey");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdIsNull() {
            addCriterion("arena_exp_id is null");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdIsNotNull() {
            addCriterion("arena_exp_id is not null");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdEqualTo(Long value) {
            addCriterion("arena_exp_id =", value, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdNotEqualTo(Long value) {
            addCriterion("arena_exp_id <>", value, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdGreaterThan(Long value) {
            addCriterion("arena_exp_id >", value, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdGreaterThanOrEqualTo(Long value) {
            addCriterion("arena_exp_id >=", value, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdLessThan(Long value) {
            addCriterion("arena_exp_id <", value, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdLessThanOrEqualTo(Long value) {
            addCriterion("arena_exp_id <=", value, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdIn(List<Long> values) {
            addCriterion("arena_exp_id in", values, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdNotIn(List<Long> values) {
            addCriterion("arena_exp_id not in", values, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdBetween(Long value1, Long value2) {
            addCriterion("arena_exp_id between", value1, value2, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andArenaExpIdNotBetween(Long value1, Long value2) {
            addCriterion("arena_exp_id not between", value1, value2, "arenaExpId");
            return (Criteria) this;
        }

        public Criteria andGroupConfigIsNull() {
            addCriterion("group_config is null");
            return (Criteria) this;
        }

        public Criteria andGroupConfigIsNotNull() {
            addCriterion("group_config is not null");
            return (Criteria) this;
        }

        public Criteria andGroupConfigEqualTo(String value) {
            addCriterion("group_config =", value, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigNotEqualTo(String value) {
            addCriterion("group_config <>", value, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigGreaterThan(String value) {
            addCriterion("group_config >", value, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigGreaterThanOrEqualTo(String value) {
            addCriterion("group_config >=", value, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigLessThan(String value) {
            addCriterion("group_config <", value, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigLessThanOrEqualTo(String value) {
            addCriterion("group_config <=", value, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigLike(String value) {
            addCriterion("group_config like", value, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigNotLike(String value) {
            addCriterion("group_config not like", value, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigIn(List<String> values) {
            addCriterion("group_config in", values, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigNotIn(List<String> values) {
            addCriterion("group_config not in", values, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigBetween(String value1, String value2) {
            addCriterion("group_config between", value1, value2, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andGroupConfigNotBetween(String value1, String value2) {
            addCriterion("group_config not between", value1, value2, "groupConfig");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andEditorIsNull() {
            addCriterion("editor is null");
            return (Criteria) this;
        }

        public Criteria andEditorIsNotNull() {
            addCriterion("editor is not null");
            return (Criteria) this;
        }

        public Criteria andEditorEqualTo(String value) {
            addCriterion("editor =", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotEqualTo(String value) {
            addCriterion("editor <>", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorGreaterThan(String value) {
            addCriterion("editor >", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorGreaterThanOrEqualTo(String value) {
            addCriterion("editor >=", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLessThan(String value) {
            addCriterion("editor <", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLessThanOrEqualTo(String value) {
            addCriterion("editor <=", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorLike(String value) {
            addCriterion("editor like", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotLike(String value) {
            addCriterion("editor not like", value, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorIn(List<String> values) {
            addCriterion("editor in", values, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotIn(List<String> values) {
            addCriterion("editor not in", values, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorBetween(String value1, String value2) {
            addCriterion("editor between", value1, value2, "editor");
            return (Criteria) this;
        }

        public Criteria andEditorNotBetween(String value1, String value2) {
            addCriterion("editor not between", value1, value2, "editor");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}