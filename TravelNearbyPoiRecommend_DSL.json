{"tasks": [{"url": "com.sankuai.hbdata.travelsearch.ranking@TravelRankService", "alias": "travelRank", "taskType": "ThriftGeneric", "method": "searchRank", "timeout": 10000, "inputs": {"request": {"query": "#queryContent", "source": "SEARCH_MT_CHANNEL", "userId": 0, "uid": "<#if userId?? && userId?trim != ''>${userId?number}<#else>0</#if>", "cityId": "#cityId", "sort": "<#if orderType??><#if orderType == 1>SMART<#elseif orderType == 2>DISTANCE<#elseif orderType == 3>POPULARITY<#elseif orderType == 4>PRICE<#elseif orderType == 5>RATING<#else>SMART</#if><#else>SMART</#if>", "offset": 0, "limit": 10, "multiSearchType": "poiid_list", "appContext": {"location": {"lat": "#latitude", "lng": "#longitude"}}}}, "inputsExtra": {"request": "com.meituan.hbdata.travel.ranking.thrift.rank.TravelRequest"}}], "name": "queryTravelNearbyPoiRecommend", "description": "根据位置信息推荐附近的旅游POI", "outputs": {"result": "<#if travelRank?? && travelRank.status?? && travelRank.status == 'OK' && travelRank.rankedItemList??>[<#list travelRank.rankedItemList as item>${item.id}<#sep>,</#sep></#list>]<#else>[]</#if>"}}