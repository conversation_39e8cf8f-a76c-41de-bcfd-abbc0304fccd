/*
 * Copyright (c) 2024 meituan.com
 * All rights reserved.
 *
 */
package com.sankuai.gaigc.arrange.config.mccconfig;

import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 组件模型降级配置
 *
 * <AUTHOR>
 * @created 2024/7/1
 */
@Data
public class ComponentModelDegradeConfig {
    /** 是否开启降级 */
    private boolean openDegrade;
    /** 是否开启应用维度降级 */
    private boolean openAppDimDegrade;
    /** 模型参数名称 */
    private List<String> modelParamNames;
    /** 降级模型映射 */
    private Map<String, String> degradeModelMapping = Maps.newHashMap();
}
