package com.sankuai.gaigc.arrange.common.core.service;

import com.meituan.ut.toolkit.mock.util.OctoMockUtils;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: wenhao10
 * @Description:
 * @Date: 2023-08-17 12:59
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {ApplicationLoader.class})
public abstract class BaseSpringTest {
    static {
        //针对OCTO的注册逻辑进行Mock
        OctoMockUtils.mockRegister();
    }
    @Before
    public void setUp() throws Exception {
    }
}
