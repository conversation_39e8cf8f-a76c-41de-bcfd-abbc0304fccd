package com.sankuai.gaigc.arrange.common.core.service;

import com.sankuai.gaigc.arrange.api.context.Context;
import com.sankuai.gaigc.arrange.api.enums.CategoryEnum;
import com.sankuai.gaigc.arrange.api.enums.ParamTypeEnum;
import com.sankuai.gaigc.arrange.common.core.promptflow.component.comp.DataCache;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentInfo;
import com.sankuai.gaigc.arrange.common.core.promptflow.param.ComponentParam;
import com.sankuai.gaigc.arrange.common.core.taskflow.DagContextHolder;
import com.sankuai.gaigc.arrange.common.core.taskflow.context.DagContext;
import com.sankuai.gaigc.arrange.common.core.taskflow.enums.ResultState;
import com.sankuai.gaigc.arrange.common.core.taskflow.operator.OperatorResult;
import com.sankuai.gaigc.arrange.dao.dal.external.cellar.ICellarRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DataCacheTest {

    @InjectMocks
    private DataCache dataCache;

    @Mock
    private ICellarRepository cellarRepository;

    private Context context;
    private ComponentInfo componentInfo;

    @Before
    public void setUp() {
        context = new Context();
        componentInfo = new ComponentInfo();
    }


    /**
     * jsonPath拼接
     * @throws Exception
     */
    @Test
    public void testKeyHasJsonPath() throws Exception {
        DagContext ctx = new DagContext();
        OperatorResult operatorResult = new OperatorResult("123", ResultState.SUCCESS);
        ctx.putOperatorResult("userId",operatorResult);
        DagContextHolder.set(ctx);
        OperatorResult userId = DagContextHolder.get().getOperatorResult("userId");

        assertNotNull(userId);

        Map<String, Object> fields = new HashMap<>();
        fields.put("field1", "value1");
        fields.put("field2", "value2");

        Map<String, ComponentParam> inputs = new HashMap<>();
        inputs.put("key", new ComponentParam("key", ParamTypeEnum.STRING, "key_$userId", true, "缓存key", CategoryEnum.DEFAULT));
        inputs.put("expireTime", new ComponentParam("expireTime", ParamTypeEnum.INT, "60", true, "过期时间(单位：秒)", CategoryEnum.DEFAULT));
        inputs.put("fields", new ComponentParam("fields", ParamTypeEnum.MAP, fields, true, "要缓存的数据", CategoryEnum.CUSTOM));
        inputs.put("userId", new ComponentParam("userId", ParamTypeEnum.INT, fields, true, "123", CategoryEnum.CUSTOM));
        componentInfo.setInputs(inputs);

        Map<String, Object> result = dataCache.execute(context, componentInfo);

        verify(cellarRepository, times(1)).cellarCacheComponentWrite(anyString(), anyString(), any(), anyInt());
        assertEquals(fields, result.get("cachedData"));
        assertEquals("key_123", result.get("cachedDataKey"));
    }


    /**
     * 写入常量字段全等
     * @throws Exception
     */
    @Test
    public void testConstantFields() throws Exception {
        Map<String, Object> fields = new HashMap<>();
        fields.put("field1", "value1");
        fields.put("field2", "value2");

        Map<String, ComponentParam> inputs = new HashMap<>();
        inputs.put("key", new ComponentParam("key", ParamTypeEnum.STRING, "key", true, "缓存key", CategoryEnum.DEFAULT));
        inputs.put("expireTime", new ComponentParam("expireTime", ParamTypeEnum.INT, "60", true, "过期时间(单位：秒)", CategoryEnum.DEFAULT));
        inputs.put("fields", new ComponentParam("fields", ParamTypeEnum.MAP, fields, true, "要缓存的数据", CategoryEnum.CUSTOM));
        componentInfo.setInputs(inputs);

        Map<String, Object> result = dataCache.execute(context, componentInfo);

        verify(cellarRepository, times(1)).cellarCacheComponentWrite(anyString(), anyString(), any(), anyInt());
        assertEquals(fields, result.get("cachedData"));
    }


    /**
     * 写入变量字段全等
     * @throws Exception
     */
    @Test
    public void testVariableFields() throws Exception {
        DagContext ctx = new DagContext();
        OperatorResult operatorResult = new OperatorResult("123", ResultState.SUCCESS);
        ctx.putOperatorResult("userId",operatorResult);
        DagContextHolder.set(ctx);
        OperatorResult userId = DagContextHolder.get().getOperatorResult("userId");

        assertNotNull(userId);

        Map<String, Object> fields = new HashMap<>();
        fields.put("field1", "value1");
        fields.put("field2", "$userId");

        Map<String, ComponentParam> inputs = new HashMap<>();
        inputs.put("key", new ComponentParam("key", ParamTypeEnum.STRING, "key_$userId", true, "缓存key", CategoryEnum.DEFAULT));
        inputs.put("expireTime", new ComponentParam("expireTime", ParamTypeEnum.INT, "60", true, "过期时间(单位：秒)", CategoryEnum.DEFAULT));
        inputs.put("fields", new ComponentParam("fields", ParamTypeEnum.MAP, fields, true, "要缓存的数据", CategoryEnum.CUSTOM));
        inputs.put("userId", new ComponentParam("userId", ParamTypeEnum.INT, fields, true, "123", CategoryEnum.CUSTOM));
        componentInfo.setInputs(inputs);

        Map<String, Object> result = dataCache.execute(context, componentInfo);

        verify(cellarRepository, times(1)).cellarCacheComponentWrite(anyString(), anyString(), any(), anyInt());

        Map<String, Object> cachedData = (Map<String, Object>) result.get("cachedData");
        assertEquals("123", cachedData.get("field2").toString());
    }




}