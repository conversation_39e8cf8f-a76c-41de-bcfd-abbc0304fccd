package SpringBoot

import com.sankuai.gaigc.arrange.base.SpringBootSpecification
import com.sankuai.gaigc.arrange.common.core.promptflow.beans.PromptFlowManager
import com.sankuai.gaigc.arrange.common.core.promptflow.dsl.Flow
import com.sankuai.gaigc.arrange.common.core.promptflow.entity.FlowIdVersionKeyFlowSnapshotCacheKey
import com.sankuai.gaigc.arrange.dao.dal.entity.AigcFlowSnapshot
import com.sankuai.gaigc.arrange.dao.dal.example.AigcFlowSnapshotExample
import com.sankuai.gaigc.arrange.dao.dal.mapper.AigcFlowSnapshotMapper
import org.apache.commons.collections4.MapUtils
import spock.lang.Requires

import javax.annotation.Resource

@Requires({ System.getProperty("os.name").contains("Mac OS X") })
class PromptFlowManagerSpringBootSpec extends SpringBootSpecification {
    @Resource
    private PromptFlowManager promptFlowManager;
    @Resource
    private AigcFlowSnapshotMapper aigcFlowSnapshotMapper;

    def getFlowByIdAndVersionIfItIsInTheCache() {
        given:
        def flowId = 2293639L
        def version = 1
        and:
        def example = new AigcFlowSnapshotExample()
        example.createCriteria().andFlowIdEqualTo(flowId).andVersionEqualTo(version)
        def example1 = aigcFlowSnapshotMapper.selectByExample(example)
        AigcFlowSnapshot aigcFlowSnapshot = example1.get(0)
        when:
        Flow flow = promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(flowId, version)
        then:
        aigcFlowSnapshot.getFlowId().toString() == flow.getId()
    }

    def getFlowByIdIfItIsInTheCache() {
        given:
        true
        and:
        def example = new AigcFlowSnapshotExample()
        example.createCriteria().andStatusEqualTo(1)
                .andFlowIdEqualTo(494)

        def example1 = aigcFlowSnapshotMapper.selectByExample(example)
        AigcFlowSnapshot aigcFlowSnapshot = example1.get(0)
        when:

        Flow flow = promptFlowManager.getFlowByIdIfItIsInTheCache(aigcFlowSnapshot.getFlowId())
        then:
        aigcFlowSnapshot.getFlowId().toString() == flow.getId()
    }

    def 循环getFlowByIdIfItIsInTheCache() {
        given:
        true
        and:
        def example = new AigcFlowSnapshotExample()
        example.createCriteria().andStatusEqualTo(1)
        def example1 = aigcFlowSnapshotMapper.selectByExample(example)
        when:
        for (final def a in example1) {
            Flow flow = promptFlowManager.getFlowByIdIfItIsInTheCache(a.getFlowId())

            if (!a.getFlowId().toString().equals(flow.getId())) {
                log.info("flowId:{},flowId:{}", a.getFlowId(), flow.getId())
                throw new Exception("结果不一样")
            }
        }
        then:
        example1 != null
    }

    def removeFlowIdVersionKeyFlowSnapshotCache() {
        given:
        Long flowId = 2293639
        Integer version = 1
        when:
        Flow flow = promptFlowManager.getFlowByIdAndVersionIfItIsInTheCache(flowId, version)
        def key = new FlowIdVersionKeyFlowSnapshotCacheKey()
        key.setFlowId(flowId)
        key.setVersion(version)
        def get = PromptFlowManager.FLOW_ID_VERSION_KEY_FLOW_SNAPSHOT_CACHE.get(key)
        if (get == null || flow == null) {
            throw new Exception("缓存为空")
        }
        def flow_id_version_key_flow_snapshot_cache2 = PromptFlowManager.FLOW_ID_VERSION_KEY_FLOW_SNAPSHOT_CACHE
        def map2 = flow_id_version_key_flow_snapshot_cache2.asMap()
        super.printJsonLog(map2)
        promptFlowManager.removeFlowIdVersionKeyFlowSnapshotCache(key)
        def flow_id_version_key_flow_snapshot_cache = PromptFlowManager.FLOW_ID_VERSION_KEY_FLOW_SNAPSHOT_CACHE
        def map = flow_id_version_key_flow_snapshot_cache.asMap()
        super.printJsonLog(map)
        then:
        map != null
    }


    def removeFlowIdKeyFlowSnapshotCache() {
        given:
        def flow_id_key_flow_snapshot_cache = PromptFlowManager.FLOW_ID_KEY_FLOW_SNAPSHOT_CACHE
        when:
        def example = new AigcFlowSnapshotExample()
        example.createCriteria().andStatusEqualTo(1)
        def flowId = aigcFlowSnapshotMapper.selectByExample(example).get(0).getFlowId()
        and:
        Flow flow = promptFlowManager.getFlowByIdIfItIsInTheCache(flowId)
        and:
        def get = flow_id_key_flow_snapshot_cache.get(flowId)
        if (get == null || flow == null) {
            throw new Exception("缓存为空")
        }
        and:
        def map1 = flow_id_key_flow_snapshot_cache.asMap()
        super.printJsonLog(map1)
        if (MapUtils.isEmpty(map1)) {
            throw new Exception("缓存为空")
        }
        and: "移除缓存 正常就是应该为空"
        promptFlowManager.removeFlowIdKeyFlowSnapshotCache(flowId)
        def map2 = flow_id_key_flow_snapshot_cache.asMap()
        if (MapUtils.isNotEmpty(map2)) {
            throw new Exception("缓存不为空")
        }
        super.printJsonLog(map2)
        then:
        map2 != null
    }

}
