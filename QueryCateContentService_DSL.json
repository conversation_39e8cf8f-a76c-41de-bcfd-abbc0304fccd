{"tasks": [{"url": "com.sankuai.cms.delivery.api@CateLaunchThrift", "alias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "taskType": "ThriftGeneric", "method": "queryContentList", "timeout": 10000, "inputs": {"request": {"boothId": 5, "cateType": 18, "channel": 3, "channelVersion": 2, "deviceType": 2, "needSubContents": true, "noAllIcon": false, "platform": 1, "wechatDP": false, "cityId": "#cityId", "userId": "#userId", "version": "<#if version?? && version?trim != ''>${version}<#else>12.22.405</#if>"}}, "inputsExtra": {"request": "com.meituan.nibhtp.os.htp.cms.delivery.dto.LaunchQueryRequest"}}], "name": "queryContentList", "description": "查询美食内容投放信息", "outputs": {"result": "<#if cateQuery?? && cateQuery.cateThriftModels??>[<#list cateQuery.cateThriftModels as model><#if model??>{\"cateId\":${model.cateId!0},\"name\":\"${model.name!''}\",\"subModelList\":<#if model.subModelList??>[<#list model.subModelList as sub><#if sub??>{\"cateId\":${sub.cateId!0},\"parentId\":${sub.parentId!0},\"name\":\"${sub.name!''}\"}</#if><#sep>,</#sep></#list>]<#else>[]</#if>}</#if><#sep>,</#sep></#list>]<#else>[]</#if>"}}